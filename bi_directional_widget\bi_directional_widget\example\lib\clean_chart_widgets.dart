import 'dart:convert';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:graphic/graphic.dart' as graphic;

/// Chart size enumeration
enum ChartSize { small, medium, large }

/// Chart type enumeration
enum ChartType { pie, donut, bar, scatter, bubble, histogram }

/// Clean Chart Widgets - Factory methods for creating standalone chart widgets
/// Following the calendar pattern with size variants and JSON serialization
class CleanChartWidgets {
  // Default blue color palette for consistent theming
  static const List<Color> _defaultColorList = [
    Color(0xFF0D47A1), // Dark Blue
    Color(0xFF1565C0), // Medium Dark Blue
    Color(0xFF1976D2), // Blue
    Color(0xFF1E88E5), // Medium Blue
    Color(0xFF2196F3), // Light Blue
    Color(0xFF42A5F5), // Lighter Blue
    Color(0xFF64B5F6), // Very Light Blue
    Color(0xFF90CAF9), // Lightest Blue
  ];

  /// Create pie chart widget
  static Widget createPieChart({
    ChartSize size = ChartSize.medium,
    String title = "Temperature theme",
    List<Color>? colors,
  }) {
    return _createPieChart(size, title, colors ?? _defaultColorList);
  }

  /// Create donut chart widget
  static Widget createDonutChart({
    ChartSize size = ChartSize.medium,
    String title = "Temperature theme",
    List<Color>? colors,
  }) {
    return _createDonutChart(size, title, colors ?? _defaultColorList);
  }

  /// Create bar chart widget
  static Widget createBarChart({
    ChartSize size = ChartSize.medium,
    String title = "Temperature theme",
    List<Color>? colors,
  }) {
    return _createBarChart(size, title, colors ?? _defaultColorList);
  }

  /// Create scatter chart widget
  static Widget createScatterChart({
    ChartSize size = ChartSize.medium,
    String title = "Temperature theme",
    List<Color>? colors,
  }) {
    return _createScatterChart(size, title, colors ?? _defaultColorList);
  }

  /// Create bubble chart widget
  static Widget createBubbleChart({
    ChartSize size = ChartSize.medium,
    String title = "Bubble Analysis",
    List<Color>? colors,
  }) {
    return _createBubbleChart(size, title, colors ?? _defaultColorList);
  }

  /// Create histogram chart widget
  static Widget createHistogramChart({
    ChartSize size = ChartSize.medium,
    String title = "Temperature theme",
    List<Color>? colors,
  }) {
    return _createHistogramChart(size, title, colors ?? _defaultColorList);
  }

  // Size configuration helpers
  static double _getChartSize(ChartSize size) {
    switch (size) {
      case ChartSize.small:
        return 200;
      case ChartSize.medium:
        return 300;
      case ChartSize.large:
        return 400;
    }
  }

  static double _getFontSize(ChartSize size) {
    switch (size) {
      case ChartSize.small:
        return 10;
      case ChartSize.medium:
        return 12;
      case ChartSize.large:
        return 14;
    }
  }

  // Chart data generators
  static List<Map<String, dynamic>> _getPieChartData() {
    return [
      {'category': 'Point 1', 'value': 25.0, 'index': 0},
      {'category': 'Point 2', 'value': 20.0, 'index': 1},
      {'category': 'Point 3', 'value': 15.0, 'index': 2},
      {'category': 'Point 4', 'value': 10.0, 'index': 3},
      {'category': 'Point 5', 'value': 12.0, 'index': 4},
      {'category': 'Point 6', 'value': 8.0, 'index': 5},
      {'category': 'Point 7', 'value': 6.0, 'index': 6},
      {'category': 'Point 8', 'value': 4.0, 'index': 7},
    ];
  }

  static List<Map<String, dynamic>> _getBarChartData() {
    return [
      {'category': 'Jan', 'value': 80.0, 'index': 0},
      {'category': 'Feb', 'value': 90.0, 'index': 1},
      {'category': 'Mar', 'value': 70.0, 'index': 2},
      {'category': 'Apr', 'value': 85.0, 'index': 3},
      {'category': 'May', 'value': 95.0, 'index': 4},
      {'category': 'Jun', 'value': 75.0, 'index': 5},
    ];
  }

  static List<Map<String, dynamic>> _getScatterChartData() {
    return [
      {'x': 5.0, 'y': 3000.0, 'index': 0},
      {'x': 10.0, 'y': 4500.0, 'index': 1},
      {'x': 15.0, 'y': 6000.0, 'index': 2},
      {'x': 20.0, 'y': 5500.0, 'index': 3},
      {'x': 25.0, 'y': 7000.0, 'index': 4},
      {'x': 30.0, 'y': 6500.0, 'index': 5},
    ];
  }

  static List<Map<String, dynamic>> _getBubbleChartData() {
    return [
      {'x': 20.0, 'y': 25.0, 'size': 15.0, 'index': 0},
      {'x': 40.0, 'y': 30.0, 'size': 25.0, 'index': 1},
      {'x': 60.0, 'y': 35.0, 'size': 20.0, 'index': 2},
      {'x': 80.0, 'y': 40.0, 'size': 30.0, 'index': 3},
    ];
  }

  static List<Map<String, dynamic>> _getHistogramData(ChartSize size) {
    // Size-responsive X-axis values based on requirements
    List<double> xValues;
    switch (size) {
      case ChartSize.small:
        xValues = [0, 10, 20, 30, 40];
        break;
      case ChartSize.medium:
        xValues = [0, 5, 10, 15, 20, 25, 30, 35];
        break;
      case ChartSize.large:
        xValues = [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55];
        break;
    }

    // Generate histogram data with varying frequencies
    return xValues.asMap().entries.map((entry) {
      final index = entry.key;
      final value = entry.value;
      final frequency = 1.0 + (math.sin(index * 0.5) + 1) * 1.5; // Varying frequency
      return {
        'value': value,
        'frequency': frequency,
        'index': index,
      };
    }).toList();
  }

  /// Create clean pie chart widget using graphic package
  static Widget _createPieChart(ChartSize size, String title, List<Color> colors) {
    final data = _getPieChartData();
    final chartSize = _getChartSize(size);
    final fontSize = _getFontSize(size);
    final effectiveColorList = colors.take(data.length).toList();

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: fontSize + 2,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              // Chart
              Expanded(
                child: Container(
                  width: chartSize,
                  height: chartSize,
                  child: graphic.Chart(
                    data: data,
                    variables: {
                      'category': graphic.Variable(
                        accessor: (dynamic map) => (map as Map)['category'] as String,
                      ),
                      'value': graphic.Variable(
                        accessor: (dynamic map) => (map as Map)['value'] as num,
                        scale: graphic.LinearScale(min: 0),
                      ),
                      'index': graphic.Variable(
                        accessor: (dynamic map) => (map as Map)['index'] as num,
                      ),
                    },
                    marks: [
                      graphic.IntervalMark(
                        position: graphic.Varset('value'),
                        color: graphic.ColorEncode(variable: 'index', values: effectiveColorList),
                        size: graphic.SizeEncode(value: 1.0), // Full pie chart - no inner hole
                      ),
                    ],
                    coord: graphic.PolarCoord(
                      transposed: true,
                      dimCount: 1,
                    ),
                  ),
                ),
              ),
              // Legend
              Expanded(
                child: _buildLegend(data, effectiveColorList, fontSize),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Create clean donut chart widget using graphic package
  static Widget _createDonutChart(ChartSize size, String title, List<Color> colors) {
    final data = _getPieChartData();
    final chartSize = _getChartSize(size);
    final fontSize = _getFontSize(size);
    final effectiveColorList = colors.take(data.length).toList();

    Widget chartWidget = Container(
      width: chartSize,
      height: chartSize,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Main donut chart using graphic package
          graphic.Chart(
            data: data,
            variables: {
              'category': graphic.Variable(
                accessor: (dynamic map) => (map as Map)['category'] as String,
              ),
              'value': graphic.Variable(
                accessor: (dynamic map) => (map as Map)['value'] as num,
                scale: graphic.LinearScale(min: 0),
              ),
              'index': graphic.Variable(
                accessor: (dynamic map) => (map as Map)['index'] as num,
              ),
            },
            marks: [
              graphic.IntervalMark(
                position: graphic.Varset('value'),
                color: graphic.ColorEncode(variable: 'index', values: effectiveColorList),
                size: graphic.SizeEncode(value: 0.5), // Creates the donut hole
              ),
            ],
            coord: graphic.PolarCoord(
              transposed: true,
              dimCount: 1,
            ),
          ),
          // Center text overlay
          Container(
            width: 100.0,
            height: 100.0,
            decoration: const BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
            ),
            child: const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    "Total Value",
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 8.0,
                      color: Colors.grey,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    "\$9,999.99",
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 12.0,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );

    Widget legendWidget = _buildLegend(data, effectiveColorList, fontSize);

    // Responsive legend positioning
    Widget content;
    switch (size) {
      case ChartSize.small:
        // Legend below chart
        content = Column(
          children: [
            chartWidget,
            const SizedBox(height: 16),
            legendWidget,
          ],
        );
        break;
      case ChartSize.medium:
      case ChartSize.large:
        // Legend on the right
        content = Row(
          children: [
            chartWidget,
            const SizedBox(width: 16),
            Expanded(child: legendWidget),
          ],
        );
        break;
    }

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: fontSize + 2,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          content,
        ],
      ),
    );
  }

  /// Create clean bar chart widget using graphic package
  static Widget _createBarChart(ChartSize size, String title, List<Color> colors) {
    final data = _getBarChartData();
    final chartSize = _getChartSize(size);
    final fontSize = _getFontSize(size);
    final effectiveColorList = colors.take(data.length).toList();

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: fontSize + 2,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          Container(
            height: chartSize * 0.8,
            child: graphic.Chart(
              data: data,
              variables: {
                'category': graphic.Variable(
                  accessor: (dynamic map) => (map as Map)['category'] as String,
                ),
                'value': graphic.Variable(
                  accessor: (dynamic map) => (map as Map)['value'] as num,
                  scale: graphic.LinearScale(min: 0, max: 120, tickCount: 7),
                ),
                'index': graphic.Variable(
                  accessor: (dynamic map) => (map as Map)['index'] as num,
                ),
              },
              marks: [
                graphic.IntervalMark(
                  position: graphic.Varset('category') * graphic.Varset('value'),
                  color: graphic.ColorEncode(variable: 'index', values: effectiveColorList),
                  size: graphic.SizeEncode(value: 0.8),
                ),
              ],
              axes: [graphic.Defaults.horizontalAxis, graphic.Defaults.verticalAxis],
              coord: graphic.RectCoord(),
            ),
          ),
        ],
      ),
    );
  }

  /// Create clean scatter chart widget using graphic package
  static Widget _createScatterChart(ChartSize size, String title, List<Color> colors) {
    final data = _getScatterChartData();
    final chartSize = _getChartSize(size);
    final fontSize = _getFontSize(size);
    final effectiveColorList = colors.take(data.length).toList();

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: fontSize + 2,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          Container(
            height: chartSize * 0.8,
            child: graphic.Chart(
              data: data,
              variables: {
                'x': graphic.Variable(
                  accessor: (Map map) => map['x'] as num,
                  scale: graphic.LinearScale(min: 0, max: 35, tickCount: 8),
                ),
                'y': graphic.Variable(
                  accessor: (Map map) => map['y'] as num,
                  scale: graphic.LinearScale(min: 2000, max: 8000, tickCount: 7),
                ),
                'index': graphic.Variable(accessor: (Map map) => map['index'] as num),
              },
              marks: [
                graphic.PointMark(
                  position: graphic.Varset('x') * graphic.Varset('y'),
                  size: graphic.SizeEncode(value: 8.0),
                  color: graphic.ColorEncode(variable: 'index', values: effectiveColorList),
                  shape: graphic.ShapeEncode(value: graphic.CircleShape()),
                ),
              ],
              axes: [graphic.Defaults.horizontalAxis, graphic.Defaults.verticalAxis],
              coord: graphic.RectCoord(),
            ),
          ),
        ],
      ),
    );
  }

  /// Create clean bubble chart widget using graphic package
  static Widget _createBubbleChart(ChartSize size, String title, List<Color> colors) {
    final data = _getBubbleChartData();
    final chartSize = _getChartSize(size);
    final fontSize = _getFontSize(size);
    final effectiveColorList = colors.take(data.length).toList();

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: fontSize + 2,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              // Chart
              Expanded(
                flex: 2,
                child: Container(
                  height: chartSize * 0.8,
                  child: graphic.Chart(
                    data: data,
                    variables: {
                      'x': graphic.Variable(
                        accessor: (dynamic map) => (map as Map)['x'] as num,
                        scale: graphic.LinearScale(min: 0, max: 100, tickCount: 6),
                      ),
                      'y': graphic.Variable(
                        accessor: (dynamic map) => (map as Map)['y'] as num,
                        scale: graphic.LinearScale(min: 0, max: 50, tickCount: 6),
                      ),
                      'size': graphic.Variable(
                        accessor: (dynamic map) => (map as Map)['size'] as num,
                      ),
                      'index': graphic.Variable(
                        accessor: (dynamic map) => (map as Map)['index'] as num,
                      ),
                    },
                    marks: [
                      graphic.PointMark(
                        position: graphic.Varset('x') * graphic.Varset('y'),
                        size: graphic.SizeEncode(
                          variable: 'size',
                          values: [5, 10, 15, 20, 25, 30, 35, 40], // Size range for bubbles
                        ),
                        color: graphic.ColorEncode(variable: 'index', values: effectiveColorList),
                        shape: graphic.ShapeEncode(value: graphic.CircleShape()),
                      ),
                    ],
                    axes: [graphic.Defaults.horizontalAxis, graphic.Defaults.verticalAxis],
                    coord: graphic.RectCoord(),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(flex: 1, child: _buildBubbleLegend(effectiveColorList, fontSize)),
            ],
          ),
        ],
      ),
    );
  }

  /// Create clean histogram chart widget using graphic package
  static Widget _createHistogramChart(ChartSize size, String title, List<Color> colors) {
    final data = _getHistogramData(size);
    final chartSize = _getChartSize(size);
    final fontSize = _getFontSize(size);
    final effectiveColorList = colors.take(data.length).toList();

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: fontSize + 2,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          Container(
            height: chartSize * 0.6,
            child: graphic.Chart(
              data: data,
              variables: {
                'value': graphic.Variable(
                  accessor: (dynamic map) => (map as Map)['value'] as num,
                ),
                'frequency': graphic.Variable(
                  accessor: (dynamic map) => (map as Map)['frequency'] as num,
                  scale: graphic.LinearScale(min: 0),
                ),
                'index': graphic.Variable(
                  accessor: (dynamic map) => (map as Map)['index'] as num,
                ),
              },
              marks: [
                graphic.IntervalMark(
                  position: graphic.Varset('value') * graphic.Varset('frequency'),
                  color: graphic.ColorEncode(variable: 'index', values: effectiveColorList),
                  size: graphic.SizeEncode(value: 0.8),
                ),
              ],
              axes: [graphic.Defaults.horizontalAxis, graphic.Defaults.verticalAxis],
              coord: graphic.RectCoord(),
            ),
          ),
        ],
      ),
    );
  }

  // Legend builders
  static Widget _buildLegend(List<Map<String, dynamic>> data, List<Color> colors, double fontSize) {
    final numRows = (data.length / 2).ceil();

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: List.generate(numRows, (rowIndex) {
        final int item1Index = rowIndex;
        final int item2Index = rowIndex + numRows;

        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 2),
          child: Row(
            children: [
              // First item
              if (item1Index < data.length) ...[
                Expanded(
                  child: _buildLegendItem(
                    color: colors[item1Index % colors.length],
                    label: data[item1Index]['category'] ?? 'Item ${item1Index + 1}',
                    fontSize: fontSize,
                  ),
                ),
              ],
              const SizedBox(width: 8),
              // Second item
              if (item2Index < data.length) ...[
                Expanded(
                  child: _buildLegendItem(
                    color: colors[item2Index % colors.length],
                    label: data[item2Index]['category'] ?? 'Item ${item2Index + 1}',
                    fontSize: fontSize,
                  ),
                ),
              ] else ...[
                const Expanded(child: SizedBox()),
              ],
            ],
          ),
        );
      }),
    );
  }

  static Widget _buildLegendItem({
    required Color color,
    required String label,
    required double fontSize,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.rectangle,
          ),
        ),
        const SizedBox(width: 6),
        Expanded(
          child: Text(
            label,
            style: TextStyle(
              fontSize: fontSize,
              color: Colors.black87,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }


  static Widget _buildBubbleLegend(List<Color> colors, double fontSize) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        // Group header
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Colors.blue.shade100,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            'Group1',
            style: TextStyle(
              fontSize: fontSize,
              fontWeight: FontWeight.w500,
              color: Colors.blue.shade700,
            ),
          ),
        ),
        const SizedBox(height: 12),
        // Legend items
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildLegendItem(
              color: colors[0],
              label: 'X-label - 50',
              fontSize: fontSize,
            ),
            const SizedBox(height: 4),
            _buildLegendItem(
              color: colors[1],
              label: 'Y-label - 50',
              fontSize: fontSize,
            ),
            const SizedBox(height: 4),
            _buildLegendItem(
              color: colors[2],
              label: 'Value 3 - 100',
              fontSize: fontSize,
            ),
          ],
        ),
      ],
    );
  }

  /// Serialize chart widget to JSON
  static String serializeToJson(ChartType chartType, ChartSize size) {
    final chartData = {
      'type': chartType.name,
      'size': size.name,
      'timestamp': DateTime.now().toIso8601String(),
      'data': _getDefaultChartData(chartType),
      'colors': _defaultColorList.map((color) => '#${color.toARGB32().toRadixString(16).padLeft(8, '0')}').toList(),
    };

    return const JsonEncoder.withIndent('  ').convert(chartData);
  }

  /// Deserialize chart widget from JSON
  static Widget? deserializeFromJson(String jsonString) {
    try {
      final Map<String, dynamic> data = json.decode(jsonString);
      final chartType = ChartType.values.firstWhere(
        (e) => e.name == data['type'],
        orElse: () => ChartType.pie,
      );
      final size = ChartSize.values.firstWhere(
        (e) => e.name == data['size'],
        orElse: () => ChartSize.medium,
      );

      switch (chartType) {
        case ChartType.pie:
          return createPieChart(size: size);
        case ChartType.donut:
          return createDonutChart(size: size);
        case ChartType.bar:
          return createBarChart(size: size);
        case ChartType.scatter:
          return createScatterChart(size: size);
        case ChartType.bubble:
          return createBubbleChart(size: size);
        case ChartType.histogram:
          return createHistogramChart(size: size);
      }
    } catch (e) {
      debugPrint('Error deserializing chart: $e');
      return null;
    }
  }

  static dynamic _getDefaultChartData(ChartType chartType) {
    switch (chartType) {
      case ChartType.pie:
      case ChartType.donut:
        return _getPieChartData();
      case ChartType.bar:
        return _getBarChartData();
      case ChartType.scatter:
        return _getScatterChartData();
      case ChartType.bubble:
        return _getBubbleChartData();
      case ChartType.histogram:
        return _getHistogramData(ChartSize.medium);
    }
  }
}
