import 'package:flutter/material.dart';
import 'package:ui_controls_library/ui_controls_library.dart' as ui_controls;

class RingPieChartDemoScreen extends StatefulWidget {
  const RingPieChartDemoScreen({super.key});

  @override
  State<RingPieChartDemoScreen> createState() => _RingPieChartDemoScreenState();
}

class _RingPieChartDemoScreenState extends State<RingPieChartDemoScreen> {
  ui_controls.ChartSizeType selectedSize = ui_controls.ChartSizeType.small;
  ui_controls.SelectedChartType selectedChartType =
      ui_controls.SelectedChartType.ring;

  String propertyType = "Properties";

  ui_controls.ChartType get selectedChartTypeFromDropdown {
    switch (chosenValue) {
      case 'Donut Chart':
        return ui_controls.ChartType.ring;
      case 'Pie Chart':
        return ui_controls.ChartType.disc;
      case 'Bubble Chart':
        return ui_controls.ChartType.bubble;
      case 'Scatter Plot':
        return ui_controls.ChartType.scatter;
      case 'Bar Chart':
        return ui_controls.ChartType.bar;
      case 'Histogram Chart':
        return ui_controls.ChartType.histogram;
      default:
        return ui_controls.ChartType.ring; // Default to donut chart
    }
  }

  ui_controls.ChartSizeConfig get currentConfig {
    switch (selectedSize) {
      case ui_controls.ChartSizeType.small:
        return ui_controls.ChartSizeConfig(
          size: ui_controls.ChartSizeType.small,
          headingFontSize: 14,
          bodyFontSize: 12,
          labelFontSize: 10,
          chartRadius: 150,
          propertyType: propertyType,
          borderThikness: 0.5,
          borderRadius: 6,
          elevation: 1.0,
          chartType: selectedChartTypeFromDropdown,
        );
      case ui_controls.ChartSizeType.medium:
        return ui_controls.ChartSizeConfig(
          size: ui_controls.ChartSizeType.medium,
          headingFontSize: 16,
          bodyFontSize: 14,
          labelFontSize: 12,
          chartRadius: 180,
          propertyType: propertyType,
          borderThikness: 1.0,
          borderRadius: 16,
          elevation: 1.5,
          chartType: selectedChartTypeFromDropdown,
        );
      case ui_controls.ChartSizeType.large:
        return ui_controls.ChartSizeConfig(
          size: ui_controls.ChartSizeType.large,
          headingFontSize: 18,
          bodyFontSize: 16,
          labelFontSize: 14,
          chartRadius: 220,
          propertyType: propertyType,
          borderThikness: 2.0,
          borderRadius: 24,
          elevation: 2.0,
          chartType: selectedChartTypeFromDropdown,
        );
    }
  }

  var chosenValue;
  List<String> chartTypeList = [
    "Donut Chart",
    "Pie Chart",
    "Bubble Chart",
    "Scatter Plot",
    "Bar Chart",
    "Histogram Chart",
  ];
@override
Widget build(BuildContext context) {
  return Scaffold(
    body: Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 20.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Dropdowns Row
          Row(
            children: [
              // Chart Type
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Chart Type',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 13,
                      color: Colors.black,
                    ),
                  ),
                  const SizedBox(height: 6),
                  Container(
                    width: 180,
                    height: 38,
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                      color: Colors.white,
                    ),
                    child: DropdownButtonHideUnderline(
                      child: DropdownButton<String>(
                        value: chosenValue ?? 'Donut Chart',
                        isExpanded: true,
                        icon: const Icon(Icons.arrow_drop_down),
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.black,
                          fontWeight: FontWeight.w500,
                        ),
                        dropdownColor: Colors.white,
                        focusColor: Colors.transparent,
                        onChanged: (value) {
                          setState(() {
                            chosenValue = value!;
                          });
                        },
                        items: chartTypeList.map((String value) {
                          return DropdownMenuItem<String>(
                            value: value,
                            child: Text(value),
                          );
                        }).toList(),
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(width: 32),

              // Chart Size
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Chart Size',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 13,
                      color: Colors.black,
                    ),
                  ),
                  const SizedBox(height: 6),
                  Container(
                    width: 140,
                    height: 38,
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                      color: Colors.white,
                    ),
                    child: DropdownButtonHideUnderline(
                      child: DropdownButton<ui_controls.ChartSizeType>(
                        value: selectedSize,
                        isExpanded: true,
                        icon: const Icon(Icons.arrow_drop_down),
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.black,
                          fontWeight: FontWeight.w500,
                        ),
                        dropdownColor: Colors.white,
                        focusColor: Colors.transparent,
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              selectedSize = value;
                            });
                          }
                        },
                        items: ui_controls.ChartSizeType.values.map((size) {
                          return DropdownMenuItem<ui_controls.ChartSizeType>(
                            value: size,
                            child: Text(size.name.toUpperCase()),
                          );
                        }).toList(),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),

          // const SizedBox(height: 24),

          // Chart Display Area
          Expanded(
            child: ui_controls.RingPieChartUIBuilder(
              config: currentConfig,
              dataMap: chartDataMap,
              colorList: chartColorList,
              onPressed: () {
                print('Chart button pressed');
              },
            ),
          ),
        ],
      ),
    ),
  );
}

  // Sample data and colors to pass to the chart
  Map<String, double> get chartDataMap => {
    'Food': 20,
    'Rent': 15,
    'Transport': 10,
    'Savings': 12,
    'Others': 8,
    'Utilities': 10,
    'Insurance': 15,
    'Entertainment': 10,
  };

  List<Color> get chartColorList => [
    const Color(0xFF0D47A1), // Dark Blue
    const Color(0xFF1565C0), // Medium Dark Blue
    const Color(0xFF1976D2), // Blue
    const Color(0xFF1E88E5), // Medium Blue
    const Color(0xFF2196F3), // Light Blue
    const Color(0xFF42A5F5), // Lighter Blue
    const Color(0xFF64B5F6), // Very Light Blue
    const Color(0xFF90CAF9), // Lightest Blue
  ];
}
