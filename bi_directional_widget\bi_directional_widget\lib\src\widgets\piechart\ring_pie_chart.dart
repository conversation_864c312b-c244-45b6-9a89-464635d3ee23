// import 'package:flutter/material.dart';
// import 'dart:convert';
// import 'package:pie_chart/pie_chart.dart';

// import '../../runtime/flexible_widget_serializer.dart';
// import 'common/config.dart';


// class RingPieChartUIBuilder extends StatefulWidget {
//   final Function onPressed;
//   final ChartSizeConfig config;

//   const RingPieChartUIBuilder({
//     super.key,
//     required this.onPressed,
//     required this.config,
//   });

//   @override
//   State<RingPieChartUIBuilder> createState() => RingPieChartUIBuilderState();
// }

// class RingPieChartUIBuilderState extends State<RingPieChartUIBuilder> {
//   late Widget screenWidgetTree;
//   String jsonOutput = '';
//   Widget? deserializedWidget;

//   final Map<String, double> dataMap = {
//     'Point 1': 1, 'Point 2': 1, 'Point 3': 1, 'Point 4': 1,
//     'Point 5': 1, 'Point 6': 1, 'Point 7': 1, 'Point 8': 1,
//   };

//   final List<Color> colorList = [
//     const Color(0xFF0D47A1), const Color(0xFF1565C0), const Color(0xFF1976D2), const Color(0xFF1E88E5),
//     const Color(0xFF42A5F5), const Color(0xFF64B5F6), const Color(0xFF90CAF9), const Color(0xFFBBDEFB),
//   ];

//   Widget buildChartSection({required ChartSizeConfig c}) {
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Text(c.size?.name.toUpperCase() ?? "", style: const TextStyle(fontSize: 24)),
//         Row(children: [
//           Expanded(
//             flex: 20,
//             child: Card(
//               elevation: c.elevation,
//               // shadowColor: Colors.black,
//               shape: RoundedRectangleBorder(
//                 borderRadius: BorderRadius.circular(c.borderRadius),
//                 side: BorderSide(
//                   color: const Color.fromARGB(255, 237, 235, 235),
//                   width: c.borderThikness ?? 0,
//                 ),
//               ),
//               child: Padding(
//                 padding: const EdgeInsets.all(16.0),
//                 child: Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     Text("Temperature theme", 
//                         style: TextStyle(fontSize: c.bodyFontSize, fontWeight: FontWeight.w600)),
//                     const SizedBox(height: 8),
//                     Row(
//                       children: [
//                         Expanded(
//                           child: PieChart(
//                             dataMap: dataMap,
//                             colorList: colorList,
//                             chartType: ChartType.ring,
//                             chartRadius: c.chartRadius ?? 80,
//                             ringStrokeWidth: (c.chartRadius ?? 80) * 0.2,
//                             centerWidget: Text(
//                               "Total Value\n\$9,999.99",
//                               textAlign: TextAlign.center,
//                               style: TextStyle(
//                                 fontWeight: FontWeight.bold,
//                                 fontSize: (c.chartRadius ?? 80) / 8,
//                               ),
//                             ),
//                             legendOptions: const LegendOptions(showLegends: false),
//                             chartValuesOptions: const ChartValuesOptions(showChartValues: false),
//                           ),
//                         ),
//                         const SizedBox(width: 16),
//                         Expanded(child: buildLegend(c.labelFontSize ?? 0)),
//                       ],
//                     ),
//                   ],
//                 ),
//               ),
//             ),
//           ),
//           const Expanded(
//             flex: 6,
//             child: SizedBox(width: 20),
//           ),
//         ]),
//         const SizedBox(height: 24),
//         Text("Properties", 
//             style: TextStyle(fontWeight: FontWeight.bold, fontSize: c.bodyFontSize, color: Colors.black)),
//         Text("Heading 1 Medium ${c.headingFontSize?.toInt()}",
//             style: TextStyle(fontSize: c.headingFontSize, fontWeight: FontWeight.w500)),
//         Text("Body 1 Regular ${c.bodyFontSize?.toInt()}",
//             style: TextStyle(fontSize: c.bodyFontSize)),
//         Text("Label - Regular ${c.labelFontSize?.toInt()}",
//             style: TextStyle(fontSize: c.labelFontSize, color: Colors.grey[700])),
//       ],
//     );
//   }

//   Widget buildLegend(double fontSize) {
//     final numRows = (dataMap.length / 2).ceil();
    
//     return Column(
//       mainAxisAlignment: MainAxisAlignment.center,
//       mainAxisSize: MainAxisSize.min,
//       children: List.generate(numRows, (rowIndex) {
//         final int item1Index = rowIndex;
//         final int item2Index = rowIndex + numRows;
//         final labels = dataMap.keys.toList();

//         return Padding(
//           padding: const EdgeInsets.symmetric(vertical: 2.0),
//           child: Row(
//             children: [
//               Expanded(
//                 child: Row(
//                   mainAxisSize: MainAxisSize.min,
//                   children: [
//                     Container(width: 12, height: 12, color: colorList[item1Index]),
//                     const SizedBox(width: 4),
//                     Expanded(
//                       child: Text(
//                         labels[item1Index], 
//                         style: TextStyle(fontSize: fontSize),
//                         overflow: TextOverflow.visible,
//                         softWrap: true,
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//               const SizedBox(width: 8),
//               Expanded(
//                 child: item2Index < labels.length
//                     ? Row(
//                         mainAxisSize: MainAxisSize.min,
//                         children: [
//                           Container(width: 12, height: 12, color: colorList[item2Index]),
//                           const SizedBox(width: 4),
//                           Expanded(
//                             child: Text(
//                               labels[item2Index], 
//                               style: TextStyle(fontSize: fontSize),
//                               overflow: TextOverflow.visible,
//                               softWrap: true,
//                             ),
//                           ),
//                         ],
//                       )
//                     : const SizedBox(), // Placeholder for alignment if odd number of items
//               ),
              
//             ],
//           ),
//         );
//       }),
//     );
//   }

//   void _rebuildWidgetTree() {
//     final c = widget.config;
//     screenWidgetTree = Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Text(c.propertyType ?? "", style: const TextStyle(fontSize: 33, fontWeight: FontWeight.bold)),
//         const SizedBox(height: 32),
//         buildChartSection(c: c),
//       ],
//     );
//   }

//   @override
//   void initState() {
//     super.initState();
//     _rebuildWidgetTree();
//   }

//   @override
//   void didUpdateWidget(covariant RingPieChartUIBuilder oldWidget) {
//     super.didUpdateWidget(oldWidget);
//     if (oldWidget.config != widget.config) {
//       setState(() => _rebuildWidgetTree());
//     }
//   }

//     Future<void> generateBuildUI() async {
//     try {
//       // Generate JSON from current widget tree
//       final generated = FlexibleWidgetSerializer.serialize(screenWidgetTree);
//       final jsonString = const JsonEncoder.withIndent('  ').convert(generated);
      
//       print("=== Ring Pie Chart Serialized Widget JSON ===\n$jsonString");
      
//       // Deserialize JSON back to widget
//       final rebuiltWidget = FlexibleWidgetSerializer.deserialize(generated);
      
//       setState(() {
//         deserializedWidget = rebuiltWidget;
//       });
      
//       print("=== JSON to Widget Conversion Complete ===");
//     } catch (e) {
//       print("Error in JSON generation/deserialization: $e");
//       setState(() {
//         jsonOutput = 'Error: $e';
//         deserializedWidget = null;
//       });
//     }
//   }

//   Future<void> generateJson() async {
//     try {
//       // Generate JSON from current widget tree
//       final generated = FlexibleWidgetSerializer.serialize(screenWidgetTree);
//       final jsonString = const JsonEncoder.withIndent('  ').convert(generated);
      
//       setState(() {
//         jsonOutput = jsonString;
//       });
      
//       print("=== JSON to Widget Conversion Complete ===");
//     } catch (e) {
//       print("Error in JSON generation/deserialization: $e");
//       setState(() {
//         jsonOutput = 'Error: $e';
//         deserializedWidget = null;
//       });
//     }
//   }

//  @override
// Widget build(BuildContext context) {
//   return SingleChildScrollView(
//     padding: const EdgeInsets.all(16),
//     child: Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         // Generate JSON Button
//         Row(
//           children: [
//             Center(
//               child: ElevatedButton.icon(
//                 onPressed: generateJson,
//                 icon: const Icon(Icons.code),
//                 label: const Text('Generate JSON'),
//                 style: ElevatedButton.styleFrom(
//                   padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
//                   textStyle: const TextStyle(fontSize: 16),
//                 ),
//               ),
//             ),
//                Center(
//               child: ElevatedButton.icon(
//                 onPressed: generateBuildUI,
//                 icon: const Icon(Icons.code),
//                 label: const Text('Generate UI from JSON'),
//                 style: ElevatedButton.styleFrom(
//                   padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
//                   textStyle: const TextStyle(fontSize: 16),
//                 ),
//               ),
//             ),
//              Center(
//               child: ElevatedButton.icon(
//                 onPressed: (){
//                   setState(() {
//                     jsonOutput='';
//                     deserializedWidget=SizedBox.shrink();
                    
//                   });
//                 },
//                 icon: const Icon(Icons.code),
//                 label: const Text('Clear'),
//                 style: ElevatedButton.styleFrom(
//                   padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
//                   textStyle: const TextStyle(fontSize: 16),
//                 ),
//               ),
//             ),
//           ],
//         ),

//         const SizedBox(height: 24),

//         // Horizontal scrollable row with widgets
//         Row(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             // Original Widget
//             Expanded(
//               child: Container(
//                 margin: const EdgeInsets.all(8),
//                 padding: const EdgeInsets.all(12),
//                 decoration: BoxDecoration(
//                   border: Border.all(color: Colors.grey.shade300),
//                   borderRadius: BorderRadius.circular(8),
//                 ),
//                 child: screenWidgetTree,
//               ),
//             ),
        
//              // JSON Output
//             Expanded(
//               child: Container(
//                 height: 400,
//                 margin: const EdgeInsets.all(8),
//                 padding: const EdgeInsets.all(12),
//                 decoration: BoxDecoration(
//                   border: Border.all(color: Colors.blue.shade200),
//                   borderRadius: BorderRadius.circular(8),
//                 ),
//                 child: SelectableText(
//                   jsonOutput.isNotEmpty ? jsonOutput : 'No JSON generated',
//                   style: const TextStyle(fontSize: 12, fontFamily: 'Courier'),
//                 ),
//               ),
//             ),
        
//             // Deserialized Widget
//             if (deserializedWidget != null)
//               Expanded(
//                 child: Container(
//                   margin: const EdgeInsets.all(8),
//                   padding: const EdgeInsets.all(12),
//                   decoration: BoxDecoration(
//                     border: Border.all(color: Colors.green.shade300),
//                     borderRadius: BorderRadius.circular(8),
//                   ),
//                   child: deserializedWidget!,
//                 ),
//               ),
        
           
//           ],
//         ),
//       ],
//     ),
//   );
// }

// }
