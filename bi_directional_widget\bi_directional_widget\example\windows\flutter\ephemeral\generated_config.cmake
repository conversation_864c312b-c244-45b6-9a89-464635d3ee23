# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\Users\\<USER>\\Downloads\\flutter_windows_3.27.0-stable\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "C:\\Users\\<USER>\\OneDrive - Brane Enterprises Pvt Limited\\Desktop\\llm_builder\\llm-builder-ui\\bi_directional_widget\\bi_directional_widget\\example" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\Users\\<USER>\\Downloads\\flutter_windows_3.27.0-stable\\flutter"
  "PROJECT_DIR=C:\\Users\\<USER>\\OneDrive - Brane Enterprises Pvt Limited\\Desktop\\llm_builder\\llm-builder-ui\\bi_directional_widget\\bi_directional_widget\\example"
  "FLUTTER_ROOT=C:\\Users\\<USER>\\Downloads\\flutter_windows_3.27.0-stable\\flutter"
  "FLUTTER_EPHEMERAL_DIR=C:\\Users\\<USER>\\OneDrive - Brane Enterprises Pvt Limited\\Desktop\\llm_builder\\llm-builder-ui\\bi_directional_widget\\bi_directional_widget\\example\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=C:\\Users\\<USER>\\OneDrive - Brane Enterprises Pvt Limited\\Desktop\\llm_builder\\llm-builder-ui\\bi_directional_widget\\bi_directional_widget\\example"
  "FLUTTER_TARGET=C:\\Users\\<USER>\\OneDrive - Brane Enterprises Pvt Limited\\Desktop\\llm_builder\\llm-builder-ui\\bi_directional_widget\\bi_directional_widget\\example\\lib\\main.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=C:\\Users\\<USER>\\OneDrive - Brane Enterprises Pvt Limited\\Desktop\\llm_builder\\llm-builder-ui\\bi_directional_widget\\bi_directional_widget\\example\\.dart_tool\\package_config.json"
)
