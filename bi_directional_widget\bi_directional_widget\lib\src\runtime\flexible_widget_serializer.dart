import 'package:flutter/material.dart';
import 'dart:convert';
import 'complete_runtime_calendar.dart';

/// Global state manager for calendar date selection
class _CalendarSelectionManager {
  static final _CalendarSelectionManager _instance = _CalendarSelectionManager._internal();
  factory _CalendarSelectionManager() => _instance;
  _CalendarSelectionManager._internal();

  String? _selectedDate;
  final List<VoidCallback> _listeners = [];

  String? get selectedDate => _selectedDate;

  void selectDate(String date) {
    _selectedDate = date;
    _notifyListeners();
  }

  void addListener(VoidCallback listener) {
    _listeners.add(listener);
  }

  void removeListener(VoidCallback listener) {
    _listeners.remove(listener);
  }

  void _notifyListeners() {
    for (final listener in _listeners) {
      listener();
    }
  }

  void reset() {
    _selectedDate = null;
    _listeners.clear();
  }
}

/// Stateful wrapper for GestureDetector that can handle interactions after deserialization
class _StatefulGestureDetector extends StatefulWidget {
  final Widget? child;
  final HitTestBehavior? behavior;

  const _StatefulGestureDetector({
    this.child,
    this.behavior,
  });

  @override
  State<_StatefulGestureDetector> createState() => _StatefulGestureDetectorState();
}

class _StatefulGestureDetectorState extends State<_StatefulGestureDetector> {
  final _selectionManager = _CalendarSelectionManager();
  String? _dateText;

  @override
  void initState() {
    super.initState();
    _extractDateText();
    _selectionManager.addListener(_onSelectionChanged);
  }

  @override
  void dispose() {
    _selectionManager.removeListener(_onSelectionChanged);
    super.dispose();
  }

  void _extractDateText() {
    if (widget.child is Container) {
      final container = widget.child as Container;
      if (container.child is Center) {
        final center = container.child as Center;
        if (center.child is Text) {
          final text = center.child as Text;
          _dateText = text.data;
        }
      }
    }
  }

  void _onSelectionChanged() {
    setState(() {
      // Rebuild when selection changes
    });
  }

  bool get _isSelected => _dateText != null && _selectionManager.selectedDate == _dateText;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // Check if this is a calendar date cell
        if (_dateText != null && int.tryParse(_dateText!) != null) {
          // This is a date cell - select this date
          _selectionManager.selectDate(_dateText!);
        }
      },
      behavior: widget.behavior,
      child: _buildChildWithSelection(),
    );
  }

  Widget _buildChildWithSelection() {
    if (widget.child is Container) {
      final container = widget.child as Container;

      // Check if this should be selected based on current state
      final shouldBeSelected = _isSelected;

      // Get the original decoration
      BoxDecoration? originalDecoration;
      if (container.decoration is BoxDecoration) {
        originalDecoration = container.decoration as BoxDecoration;
      }

      // Create new decoration based on selection state
      final newDecoration = BoxDecoration(
        color: shouldBeSelected
            ? Colors.blue
            : (originalDecoration?.color ?? Colors.transparent),
        border: originalDecoration?.border ?? Border.all(
          color: Colors.grey.shade300,
          width: 0.5
        ),
        borderRadius: originalDecoration?.borderRadius,
        boxShadow: originalDecoration?.boxShadow,
      );

      // Create new container with updated styling
      return Container(
        decoration: newDecoration,
        child: container.child is Center
            ? Center(
                child: (container.child as Center).child is Text
                    ? Text(
                        ((container.child as Center).child as Text).data ?? '',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: shouldBeSelected ? Colors.white : Colors.black,
                        ),
                      )
                    : (container.child as Center).child,
              )
            : container.child,
      );
    }
    return widget.child ?? const SizedBox();
  }
}

/// Stateful wrapper for CalendarDateCell that can handle interactions after deserialization
class _StatefulCalendarDateCell extends StatefulWidget {
  final int dayNumber;
  final bool initialIsSelected;
  final bool isToday;
  final Color? selectedColor;
  final Color? todayColor;

  const _StatefulCalendarDateCell({
    required this.dayNumber,
    required this.initialIsSelected,
    required this.isToday,
    this.selectedColor,
    this.todayColor,
  });

  @override
  State<_StatefulCalendarDateCell> createState() => _StatefulCalendarDateCellState();
}

class _StatefulCalendarDateCellState extends State<_StatefulCalendarDateCell> {
  final _selectionManager = _CalendarSelectionManager();

  @override
  void initState() {
    super.initState();
    _selectionManager.addListener(_onSelectionChanged);

    // Set initial selection if this cell was selected
    if (widget.initialIsSelected) {
      _selectionManager.selectDate(widget.dayNumber.toString());
    }
  }

  @override
  void dispose() {
    _selectionManager.removeListener(_onSelectionChanged);
    super.dispose();
  }

  void _onSelectionChanged() {
    setState(() {
      // Rebuild when selection changes
    });
  }

  bool get _isSelected => _selectionManager.selectedDate == widget.dayNumber.toString();

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        _selectionManager.selectDate(widget.dayNumber.toString());
      },
      child: Container(
        constraints: const BoxConstraints(
          minWidth: 30,
          minHeight: 30,
        ),
        decoration: BoxDecoration(
          color: _isSelected
              ? widget.selectedColor ?? Colors.blue
              : widget.isToday
                  ? widget.todayColor ?? Colors.orange.shade100
                  : Colors.transparent,
          border: Border(
            top: BorderSide(color: Colors.grey.shade300, width: 0.5),
            left: BorderSide(color: Colors.grey.shade300, width: 0.5),
            right: BorderSide(color: Colors.grey.shade300, width: 0.5),
            bottom: BorderSide(color: Colors.grey.shade300, width: 0.5),
          ),
        ),
        child: Center(
          child: Text(
            widget.dayNumber.toString(),
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ).copyWith(
              color: _isSelected
                  ? Colors.white
                  : widget.isToday
                      ? Colors.orange.shade800
                      : Colors.black,
            ),
          ),
        ),
      ),
    );
  }
}

/// Stateful wrapper for DropdownButton that can handle interactions after deserialization
class _StatefulDropdownButton extends StatefulWidget {
  final String? value;
  final bool isDense;
  final bool isExpanded;
  final double iconSize;
  final TextStyle? style;
  final List<DropdownMenuItem<String>>? items;

  const _StatefulDropdownButton({
    this.value,
    this.isDense = false,
    this.isExpanded = false,
    this.iconSize = 24.0,
    this.style,
    this.items,
  });

  @override
  State<_StatefulDropdownButton> createState() => _StatefulDropdownButtonState();
}

class _StatefulDropdownButtonState extends State<_StatefulDropdownButton> {
  late String? selectedValue;

  @override
  void initState() {
    super.initState();
    selectedValue = widget.value;
  }

  @override
  Widget build(BuildContext context) {
    return DropdownButton<String>(
      value: selectedValue,
      isDense: widget.isDense,
      isExpanded: widget.isExpanded,
      iconSize: widget.iconSize,
      style: widget.style,
      underline: const SizedBox(),
      icon: const Icon(Icons.keyboard_arrow_down, size: 16),
      items: widget.items,
      onChanged: (String? newValue) {
        setState(() {
          selectedValue = newValue;
        });
      },
    );
  }
}





/// Runtime Property Discovery Serializer
/// Can serialize any Flutter widget dynamically without custom toJson/fromJson methods
class FlexibleWidgetSerializer {
  
  /// Custom serializers for specific widget types
  static final Map<Type, Function> _customSerializers = {

    // Container serialization
    Container: (Container w) => {
      'type': 'Container',
      'width': w.constraints?.maxWidth != double.infinity ? w.constraints?.maxWidth : null,
      'height': w.constraints?.maxHeight != double.infinity ? w.constraints?.maxHeight : null,
      // Only serialize decoration if it exists, otherwise serialize color
      'decoration': w.decoration != null ? _serializeBoxDecoration(w.decoration as BoxDecoration?) : null,
      'color': w.decoration == null && w.color != null ? w.color!.toARGB32() : null,
      'padding': w.padding != null ? _serializeEdgeInsets(w.padding!) : null,
      'margin': w.margin != null ? _serializeEdgeInsets(w.margin!) : null,
      'child': w.child != null ? serialize(w.child!) : null,
    },

    // DropdownButton serialization
    DropdownButton: (DropdownButton w) => {
      'type': 'DropdownButton',
      'value': w.value?.toString(),
      'isDense': w.isDense,
      'isExpanded': w.isExpanded,
      'iconSize': w.iconSize,
      'style': w.style != null ? {
        'fontSize': w.style!.fontSize,
        'color': w.style!.color?.toARGB32(),
        'fontWeight': w.style!.fontWeight?.index,
        'fontFamily': w.style!.fontFamily,
      } : null,
      'items': w.items?.map((item) => {
        'type': 'DropdownMenuItem',
        'value': item.value?.toString(),
        'child': serialize(item.child),
      }).toList() ?? [],
    },

    // GestureDetector serialization
    GestureDetector: (GestureDetector w) => {
      'type': 'GestureDetector',
      'child': w.child != null ? serialize(w.child!) : null,
      'behavior': w.behavior?.index,
    },
    
    // Text serialization
    Text: (Text w) => {
      'type': 'Text',
      'data': w.data ?? '',
      'style': w.style != null ? {
        'fontSize': w.style!.fontSize,
        'color': w.style!.color?.toARGB32(),
        'fontWeight': w.style!.fontWeight?.index,
        'fontFamily': w.style!.fontFamily,
      } : null,
      'textAlign': w.textAlign?.index,
      'maxLines': w.maxLines,
      'overflow': w.overflow?.index,
    },
    
    // Column serialization
    Column: (Column w) => {
      'type': 'Column',
      'mainAxisAlignment': w.mainAxisAlignment.index,
      'crossAxisAlignment': w.crossAxisAlignment.index,
      'mainAxisSize': w.mainAxisSize.index,
      'children': w.children.map((child) => serialize(child)).toList(),
    },
    
    // Row serialization
    Row: (Row w) => {
      'type': 'Row',
      'mainAxisAlignment': w.mainAxisAlignment.index,
      'crossAxisAlignment': w.crossAxisAlignment.index,
      'mainAxisSize': w.mainAxisSize.index,
      'children': w.children.map((child) => serialize(child)).toList(),
    },
    
   

    // IconButton serialization
    IconButton: (IconButton w) => {
      'type': 'IconButton',
      'iconSize': w.iconSize,
      'color': w.color?.toARGB32(),
      'icon': w.icon is Icon ? {
        'type': 'Icon',
        'iconData': (w.icon as Icon).icon?.codePoint,
        'size': (w.icon as Icon).size,
        'color': (w.icon as Icon).color?.toARGB32(),
      } : null,
      'tooltip': w.tooltip,
    },

    // Icon serialization
    Icon: (Icon w) => {
      'type': 'Icon',
      'iconData': w.icon?.codePoint,
      'size': w.size,
      'color': w.color?.toARGB32(),
    },

    // Center serialization
    Center: (Center w) => {
      'type': 'Center',
      'child': w.child != null ? serialize(w.child!) : null,
    },

    // Expanded serialization
    Expanded: (Expanded w) => {
      'type': 'Expanded',
      'flex': w.flex,
      'child': serialize(w.child),
    },
    
    // SizedBox serialization
    SizedBox: (SizedBox w) => {
      'type': 'SizedBox',
      'width': w.width,
      'height': w.height,
      'child': w.child != null ? serialize(w.child!) : null,
    },
    
    // Padding serialization
    Padding: (Padding w) => {
      'type': 'Padding',
      'padding': _serializeEdgeInsets(w.padding),
      'child': w.child != null ? serialize(w.child!) : null,
    },

    // DropdownMenuItem serialization
    DropdownMenuItem: (DropdownMenuItem w) => {
      'type': 'DropdownMenuItem',
      'value': w.value?.toString(),
      'child': serialize(w.child),
    },

    // CalendarDateCell serialization - serialize as GestureDetector to avoid config issues
    CalendarDateCell: (CalendarDateCell w) => {
      'type': 'GestureDetector',
      'behavior': null,
      'child': {
        'type': 'Container',
        'decoration': {
          'color': w.isSelected
              ? (w.selectedColor?.toARGB32() ?? Colors.blue.toARGB32())
              : w.isToday
                  ? (w.todayColor?.toARGB32() ?? Colors.orange.shade100.toARGB32())
                  : Colors.transparent.toARGB32(),
          'border': 'Border.all(color: Colors.grey.shade300, width: 0.5)',
        },
        'child': {
          'type': 'Center',
          'child': {
            'type': 'Text',
            'data': w.dayNumber.toString(),
            'style': {
              'fontSize': 12.0,
              'fontWeight': 4, // FontWeight.w500
              'color': w.isSelected
                  ? Colors.white.toARGB32()
                  : w.isToday
                      ? Colors.orange.shade800.toARGB32()
                      : Colors.black.toARGB32(),
            },
          },
        },
      },
      // Store metadata for reconstruction
      '_dayNumber': w.dayNumber,
      '_isSelected': w.isSelected,
      '_isToday': w.isToday,
    },
  };
  
  /// Main serialization method - discovers widget type at runtime
  static Map<String, dynamic> serialize(Widget widget) {
    var serializer = _customSerializers[widget.runtimeType];

    // If exact type not found, try to match generic types
    if (serializer == null) {
      final widgetTypeName = widget.runtimeType.toString();
      for (var entry in _customSerializers.entries) {
        final serializerTypeName = entry.key.toString();
        // Handle generic types like DropdownButton<String> -> DropdownButton
        if (widgetTypeName.startsWith(serializerTypeName.split('<')[0])) {
          serializer = entry.value;
          break;
        }
      }
    }

    Map<String, dynamic> base = {
      'runtimeType': widget.runtimeType.toString(),
      'key': widget.key?.toString(),
      'hashCode': widget.hashCode,
    };

    if (serializer != null) {
      try {
        final serialized = serializer(widget);
        base.addAll(serialized);
      } catch (e) {
        base['serializationError'] = e.toString();
        base.addAll(_extractBasicProperties(widget));
      }
    } else {
      base['unsupported'] = true;
      base.addAll(_extractBasicProperties(widget));
    }

    return base;
  }
  
  /// Fallback property extraction for unsupported widgets
  static Map<String, dynamic> _extractBasicProperties(Widget widget) {
    return {
      'runtimeType': widget.runtimeType.toString(),
      'hashCode': widget.hashCode,
      'toString': widget.toString(),
    };
  }
  
  /// Helper: Serialize EdgeInsets
  static Map<String, dynamic> _serializeEdgeInsets(EdgeInsetsGeometry insets) {
    final resolved = insets.resolve(TextDirection.ltr);
    return {
      'left': resolved.left,
    'top': resolved.top,
    'right': resolved.right,
    'bottom': resolved.bottom,
    };
  }
  
  /// Helper: Serialize BoxDecoration
  static Map<String, dynamic>? _serializeBoxDecoration(BoxDecoration? decoration) {
    if (decoration == null) return null;

    return {
      'color': decoration.color?.toARGB32(),
      'borderRadius': decoration.borderRadius?.toString(),
      'border': decoration.border?.toString(),
      'boxShadow': decoration.boxShadow?.map((shadow) => {
        'color': shadow.color.toARGB32(),
        'offset': {'dx': shadow.offset.dx, 'dy': shadow.offset.dy},
        'blurRadius': shadow.blurRadius,
        'spreadRadius': shadow.spreadRadius,
      }).toList(),
    };
  }

  /// Helper: Serialize ShapeBorder
  static Map<String, dynamic>? _serializeShapeBorder(ShapeBorder shape) {
    if (shape is RoundedRectangleBorder) {
      return {
        'type': 'RoundedRectangleBorder',
        'borderRadius': shape.borderRadius.toString(),
        'side': shape.side != BorderSide.none ? {
          'color': shape.side.color.value,
          'width': shape.side.width,
        } : null,
      };
    }
    return {
      'type': shape.runtimeType.toString(),
      'data': shape.toString(),
    };
  }
  
  /// Convert to pretty JSON string
  static String toJsonString(Widget widget, {bool prettyPrint = false}) {
    final json = serialize(widget);
    if (prettyPrint) {
      const JsonEncoder encoder = JsonEncoder.withIndent('  ');
      return encoder.convert(json);
    }
    return jsonEncode(json);
  }
  
  /// Deserialize JSON back to Widget (comprehensive support)
  static Widget? deserialize(Map<String, dynamic> json) {
    final type = json['type'];

    switch (type) {
     

      case 'Container':
        // Handle the Flutter constraint: cannot have both color and decoration
        BoxDecoration? decoration;
        Color? color;

        if (json['decoration'] != null) {
          decoration = _parseBoxDecoration(json['decoration']);
        } else if (json['color'] != null) {
          color = Color(json['color']);
        }

        return Container(
          width: json['width']?.toDouble(),
          height: json['height']?.toDouble(),
          color: color,
          decoration: decoration,
          padding: json['padding'] != null ? _parseEdgeInsets(json['padding']) : null,
          margin: json['margin'] != null ? _parseEdgeInsets(json['margin']) : null,
          child: json['child'] != null ? deserialize(json['child']) : null,
        );

      case 'Column':
        return Column(
          mainAxisAlignment: _parseMainAxisAlignment(json['mainAxisAlignment']),
          crossAxisAlignment: _parseCrossAxisAlignment(json['crossAxisAlignment']),
          mainAxisSize: _parseMainAxisSize(json['mainAxisSize']),
          children: json['children'] != null
              ? (json['children'] as List).map((child) => deserialize(child) ?? const SizedBox()).toList()
              : [],
        );

      case 'Row':
        return Row(
          mainAxisAlignment: _parseMainAxisAlignment(json['mainAxisAlignment']),
          crossAxisAlignment: _parseCrossAxisAlignment(json['crossAxisAlignment']),
          mainAxisSize: _parseMainAxisSize(json['mainAxisSize']),
          children: json['children'] != null
              ? (json['children'] as List).map((child) => deserialize(child) ?? const SizedBox()).toList()
              : [],
        );

      case 'Expanded':
        return Expanded(
          flex: json['flex'] ?? 1,
          child: json['child'] != null ? deserialize(json['child']) ?? const SizedBox() : const SizedBox(),
        );

      case 'Text':
        final textData = json['data'];
        return Text(
          textData?.toString() ?? '', // Use empty string if data is null
          textAlign: _parseTextAlign(json['textAlign']),
          maxLines: json['maxLines'],
          overflow: _parseTextOverflow(json['overflow']),
          style: json['style'] != null ? _parseTextStyle(json['style']) : null,
        );

      case 'IconButton':
        return IconButton(
          onPressed: () {}, // Placeholder function
          icon: _parseIcon(json['icon']),
          iconSize: json['iconSize']?.toDouble(),
          color: json['color'] != null ? Color(json['color']) : null,
          tooltip: json['tooltip'],
        );

      case 'Icon':
        return _parseIcon(json);

      case 'Center':
        return Center(
          child: json['child'] != null ? deserialize(json['child']) : null,
        );

      case 'Padding':
        return Padding(
          padding: _parseEdgeInsets(json['padding']) ?? EdgeInsets.zero,
          child: json['child'] != null ? deserialize(json['child']) : null,
        );

      case 'SizedBox':
        return SizedBox(
          width: json['width']?.toDouble(),
          height: json['height']?.toDouble(),
          child: json['child'] != null ? deserialize(json['child']) : null,
        );

      case 'DropdownButton':
        return _StatefulDropdownButton(
          value: json['value'],
          isDense: json['isDense'] ?? false,
          isExpanded: json['isExpanded'] ?? false,
          iconSize: json['iconSize']?.toDouble() ?? 24.0,
          style: json['style'] != null ? _parseTextStyle(json['style']) : null,
          items: json['items'] != null
              ? (json['items'] as List).map<DropdownMenuItem<String>>((item) {
                  return DropdownMenuItem<String>(
                    value: item['value'],
                    child: deserialize(item['child']) ?? Text(item['value'] ?? ''),
                  );
                }).toList()
              : [],
        );

      case 'GestureDetector':
        // Check if this is a serialized CalendarDateCell
        if (json.containsKey('_dayNumber')) {
          return _StatefulCalendarDateCell(
            dayNumber: json['_dayNumber'] ?? 1,
            initialIsSelected: json['_isSelected'] ?? false,
            isToday: json['_isToday'] ?? false,
            selectedColor: json['_isSelected'] == true ? Colors.blue : null,
            todayColor: json['_isToday'] == true ? Colors.orange.shade100 : null,
          );
        }

        return _StatefulGestureDetector(
          behavior: json['behavior'] != null
              ? HitTestBehavior.values[json['behavior']]
              : null,
          child: json['child'] != null ? deserialize(json['child']) : null,
        );

      case 'DropdownMenuItem':
        return DropdownMenuItem<String>(
          value: json['value'],
          child: json['child'] != null ? deserialize(json['child']) ?? Text(json['value'] ?? '') : Text(json['value'] ?? ''),
        );

      default:
        // Unsupported widget type - show placeholder
        return Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.grey.shade200,
            border: Border.all(color: Colors.grey),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(
            'Unsupported: $type',
            style: const TextStyle(fontSize: 10, color: Colors.grey),
          ),
        );
    }
  }

  /// Deserialize from JSON string
  static Widget? deserializeFromString(String jsonString) {
    try {
      // Reset the calendar selection manager for new deserialization
      _CalendarSelectionManager().reset();

      final json = jsonDecode(jsonString);
      final widget = deserialize(json);

      // Try to find and set the initially selected date
      _findAndSetInitialSelection(json);

      return widget;
    } catch (e) {
      return Text('Deserialization error: $e');
    }
  }

  /// Find initially selected date from JSON and set it in the selection manager
  static void _findAndSetInitialSelection(Map<String, dynamic> json) {
    void searchForSelectedDate(dynamic data) {
      if (data is Map<String, dynamic>) {
        // Check if this is a Container with blue background (selected state)
        if (data['type'] == 'Container' && data['decoration'] != null) {
          final decoration = data['decoration'];
          if (decoration is Map<String, dynamic> && decoration['color'] != null) {
            // Check if this is a blue color (selected state)
            final color = decoration['color'];
            if (color == Colors.blue.toARGB32()) {
              // This might be a selected date cell, try to find the date text
              final child = data['child'];
              if (child is Map<String, dynamic>) {
                final dateText = _extractDateTextFromChild(child);
                if (dateText != null && int.tryParse(dateText) != null) {
                  _CalendarSelectionManager().selectDate(dateText);
                  return;
                }
              }
            }
          }
        }

        // Recursively search children
        if (data['children'] is List) {
          for (final child in data['children']) {
            searchForSelectedDate(child);
          }
        }
        if (data['child'] != null) {
          searchForSelectedDate(data['child']);
        }
      }
    }

    searchForSelectedDate(json);
  }

  /// Extract date text from a child widget structure
  static String? _extractDateTextFromChild(Map<String, dynamic> child) {
    if (child['type'] == 'Center' && child['child'] != null) {
      final centerChild = child['child'];
      if (centerChild is Map<String, dynamic> && centerChild['type'] == 'Text') {
        return centerChild['data'];
      }
    }
    return null;
  }

  // Parser methods for deserialization
  static EdgeInsets? _parseEdgeInsets(dynamic data) {
    if (data is Map<String, dynamic>) {
      return EdgeInsets.only(
        left: data['left']?.toDouble() ?? 0,
        top: data['top']?.toDouble() ?? 0,
        right: data['right']?.toDouble() ?? 0,
        bottom: data['bottom']?.toDouble() ?? 0,
      );
    }
    return null;
  }

  static BoxDecoration? _parseBoxDecoration(dynamic data) {
    if (data is Map<String, dynamic>) {
      return BoxDecoration(
        color: data['color'] != null ? Color(data['color']) : null,
        borderRadius: data['borderRadius'] != null ? _parseBorderRadius(data['borderRadius']) : null,
        border: data['border'] != null ? _parseBorder(data['border']) : null,
        boxShadow: data['boxShadow'] != null ? _parseBoxShadows(data['boxShadow']) : null,
      );
    }
    return null;
  }

  static BorderRadius? _parseBorderRadius(dynamic data) {
    // For simplicity, return a default border radius
    // In a real implementation, you'd parse the actual BorderRadius data
    return BorderRadius.circular(8.0);
  }

  static Border? _parseBorder(dynamic data) {
    // For simplicity, return a default border
    // In a real implementation, you'd parse the actual Border data
    return Border.all(color: Colors.grey.shade300, width: 1.0);
  }

  static List<BoxShadow>? _parseBoxShadows(dynamic data) {
    if (data is List) {
      return data.map((shadow) {
        if (shadow is Map<String, dynamic>) {
          return BoxShadow(
            color: shadow['color'] != null ? Color(shadow['color']) : Colors.black,
            offset: shadow['offset'] != null
                ? Offset(shadow['offset']['dx']?.toDouble() ?? 0, shadow['offset']['dy']?.toDouble() ?? 0)
                : Offset.zero,
            blurRadius: shadow['blurRadius']?.toDouble() ?? 0,
            spreadRadius: shadow['spreadRadius']?.toDouble() ?? 0,
          );
        }
        return const BoxShadow();
      }).toList();
    }
    return null;
  }

  static MainAxisAlignment _parseMainAxisAlignment(dynamic value) {
    if (value is int) {
      switch (value) {
        case 0: return MainAxisAlignment.start;
        case 1: return MainAxisAlignment.end;
        case 2: return MainAxisAlignment.center;
        case 3: return MainAxisAlignment.spaceBetween;
        case 4: return MainAxisAlignment.spaceAround;
        case 5: return MainAxisAlignment.spaceEvenly;
        default: return MainAxisAlignment.start;
      }
    }
    return MainAxisAlignment.start;
  }

  static CrossAxisAlignment _parseCrossAxisAlignment(dynamic value) {
    if (value is int) {
      switch (value) {
        case 0: return CrossAxisAlignment.start;
        case 1: return CrossAxisAlignment.end;
        case 2: return CrossAxisAlignment.center;
        case 3: return CrossAxisAlignment.stretch;
        case 4: return CrossAxisAlignment.baseline;
        default: return CrossAxisAlignment.center;
      }
    }
    return CrossAxisAlignment.center;
  }

  static MainAxisSize _parseMainAxisSize(dynamic value) {
    if (value is int) {
      switch (value) {
        case 0: return MainAxisSize.min;
        case 1: return MainAxisSize.max;
        default: return MainAxisSize.max;
      }
    }
    return MainAxisSize.max;
  }

  static TextAlign? _parseTextAlign(dynamic value) {
    if (value is int) {
      switch (value) {
        case 0: return TextAlign.left;
        case 1: return TextAlign.right;
        case 2: return TextAlign.center;
        case 3: return TextAlign.justify;
        case 4: return TextAlign.start;
        case 5: return TextAlign.end;
        default: return null;
      }
    }
    return null;
  }

  static TextOverflow? _parseTextOverflow(dynamic value) {
    if (value is int) {
      switch (value) {
        case 0: return TextOverflow.clip;
        case 1: return TextOverflow.fade;
        case 2: return TextOverflow.ellipsis;
        case 3: return TextOverflow.visible;
        default: return null;
      }
    }
    return null;
  }

  static TextStyle? _parseTextStyle(dynamic data) {
    if (data is Map<String, dynamic>) {
      return TextStyle(
        fontSize: data['fontSize']?.toDouble(),
        color: data['color'] != null ? Color(data['color']) : null,
        fontWeight: _parseFontWeight(data['fontWeight']),
        fontFamily: data['fontFamily'],
      );
    }
    return null;
  }

  static FontWeight? _parseFontWeight(dynamic value) {
    if (value is int) {
      switch (value) {
        case 0: return FontWeight.w100;
        case 1: return FontWeight.w200;
        case 2: return FontWeight.w300;
        case 3: return FontWeight.w400;
        case 4: return FontWeight.w500;
        case 5: return FontWeight.w600;
        case 6: return FontWeight.w700;
        case 7: return FontWeight.w800;
        case 8: return FontWeight.w900;
        default: return null;
      }
    }
    return null;
  }

  static Icon _parseIcon(dynamic value) {
    if (value is Map<String, dynamic>) {
      final iconData = value['iconData'];
      if (iconData != null) {
        // Map common icon code points to their respective icons
        switch (iconData) {
          case 58820: // Icons.chevron_left
            return const Icon(Icons.chevron_left);
          case 58821: // Icons.chevron_right
            return const Icon(Icons.chevron_right);
          case 57415: // Icons.calendar_today
            return const Icon(Icons.calendar_today);
          default:
            // Try to create icon from code point
            return Icon(IconData(iconData, fontFamily: 'MaterialIcons'));
        }
      }
    }

    // Fallback for string-based icon detection
    if (value != null && value.toString().contains('chevron_left')) {
      return const Icon(Icons.chevron_left);
    } else if (value != null && value.toString().contains('chevron_right')) {
      return const Icon(Icons.chevron_right);
    }

    return const Icon(Icons.help_outline);
  }

static ShapeBorder? _parseShapeBorder(dynamic data) {
  if (data is Map<String, dynamic>) {
    final type = data['type'];

    if (type == 'RoundedRectangleBorder') {
      // Parse borderRadius: supports "BorderRadius.circular(x)" string or number
      double radius = 0.0;

      final dynamic radiusData = data['borderRadius'];
      if (radiusData is String && radiusData.contains('BorderRadius.circular')) {
        final match = RegExp(r'(\d+(\.\d+)?)').firstMatch(radiusData);
        if (match != null) {
          radius = double.tryParse(match.group(0) ?? '0') ?? 0.0;
        }
      } else if (radiusData is num) {
        radius = radiusData.toDouble();
      }

      // Parse side if available
      BorderSide side = BorderSide.none;
      if (data['side'] is Map<String, dynamic>) {
        final sideData = data['side'];
        final int colorValue = sideData['color'] ?? Colors.grey.value;
        final double width = (sideData['width'] ?? 1.0).toDouble();

        side = BorderSide(color: Color(colorValue), width: width);
      }

      return RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(radius),
        side: side,
      );
    }
  }

  return null;
}




  /// Get serialization statistics
  static Map<String, dynamic> getSerializationStats(Widget widget) {
    final json = serialize(widget);
    
    int supportedWidgets = 0;
    int unsupportedWidgets = 0;
    Set<String> widgetTypes = {};
    
    void countWidgets(Map<String, dynamic> data) {
      if (data.containsKey('type')) {
        widgetTypes.add(data['type']);
        if (data.containsKey('unsupported') && data['unsupported'] == true) {
          unsupportedWidgets++;
        } else {
          supportedWidgets++;
        }
      }
      
      // Recursively count children
      if (data.containsKey('children') && data['children'] is List) {
        for (var child in data['children']) {
          if (child is Map<String, dynamic>) {
            countWidgets(child);
          }
        }
      }
      
      if (data.containsKey('child') && data['child'] is Map<String, dynamic>) {
        countWidgets(data['child']);
      }
    }
    
    countWidgets(json);
    
    return {
      'totalWidgets': supportedWidgets + unsupportedWidgets,
      'supportedWidgets': supportedWidgets,
      'unsupportedWidgets': unsupportedWidgets,
      'supportRate': supportedWidgets / (supportedWidgets + unsupportedWidgets) * 100,
      'widgetTypes': widgetTypes.toList(),
      'jsonSize': jsonEncode(json).length,
    };
  }
}
