
import 'package:flutter/material.dart';
import 'flexible_widget_serializer.dart';

/// Calendar size enumeration
enum CalendarSize { small, medium, large }

/// Configuration class for calendar sizes
class _CalendarConfig {
  final double width;
  final double height;
  final double borderWidth;
  final double borderRadius;
  final double shadowOpacity;
  final double shadowBlur;
  final double shadowOffset;
  final double headerPadding;
  final double headerFontSize;
  final String headerText;
  final String titleText;
  final double iconSize;
  final double weekdayPadding;
  final double weekdayFontSize;
  final List<String> weekdayLabels;
  final double cellMargin;
  final double cellBorderRadius;
  final double dateFontSize;
  final FontWeight dateFontWeight;
  final double labelFontSize;
  final double bodyFontSize;

  const _CalendarConfig({
    required this.width,
    required this.height,
    required this.borderWidth,
    required this.borderRadius,
    required this.shadowOpacity,
    required this.shadowBlur,
    required this.shadowOffset,
    required this.headerPadding,
    required this.headerFontSize,
    required this.labelFontSize,
    required this.headerText,
    required this.titleText,
    required this.iconSize,
    required this.bodyFontSize,
    required this.weekdayPadding,
    required this.weekdayFontSize,
    required this.weekdayLabels,
    required this.cellMargin,
    required this.cellBorderRadius,
    required this.dateFontSize,
    required this.dateFontWeight,
  });
}

/// Calendar Date Cell Widget that handles individual date interactions
class CalendarDateCell extends StatelessWidget {
  final int dayNumber;
  final bool isSelected;
  final bool isToday;
  final _CalendarConfig config;
  final Color? selectedColor;
  final Color? todayColor;
  final VoidCallback? onTap;

  const CalendarDateCell({
    super.key,
    required this.dayNumber,
    required this.isSelected,
    required this.isToday,
    required this.config,
    this.selectedColor,
    this.todayColor,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: isSelected
              ? selectedColor ?? Colors.blue
              : isToday
                  ? todayColor ?? Colors.orange.shade100
                  : Colors.transparent,
          border: Border(
            top: BorderSide(color: Colors.grey.shade300, width: 0.5),
            left: BorderSide(color: Colors.grey.shade300, width: 0.5),
            right: BorderSide(color: Colors.grey.shade300, width: 0.5),
            bottom: BorderSide(color: Colors.grey.shade300, width: 0.5),
          ),
        ),
        child: Center(
          child: Text(
            dayNumber.toString(),
            style: TextStyle(
              fontSize: config.dateFontSize,
              fontWeight: FontWeight.w500,
              color: isSelected
                  ? Colors.white
                  : isToday
                      ? Colors.orange.shade800
                      : Colors.black,
            ),
          ),
        ),
      ),
    );
  }
}

/// Complete Runtime Calendar - Uses only complete widget tree JSON approach
/// No custom serialization, only runtime discovery of complete widget hierarchy
class CompleteRuntimeCalendar {
  /// Create Calendar Widget with specified size
  static Widget createCalendar({
    required BuildContext context,
    CalendarSize size = CalendarSize.medium,
    DateTime? selectedDate,
    Color? selectedColor,
    Color? todayColor,
    Function(DateTime)? onDateSelected,
    String? selectedMonth,
    Function(String)? onMonthChanged,
  }) {
    // Size-specific configurations
    final config = _getCalendarConfig(size, context);

    // Get current month info
    final currentMonth = selectedMonth ?? 'July';
    final monthIndex = _getMonthIndex(currentMonth);
    final currentYear = selectedDate?.year ?? DateTime.now().year;
    final firstDayOfMonth = DateTime(currentYear, monthIndex, 1);
    final lastDayOfMonth = DateTime(currentYear, monthIndex + 1, 0);
    final daysInMonth = lastDayOfMonth.day;
    final firstWeekday = firstDayOfMonth.weekday; // 1 = Monday, 7 = Sunday

    return Container(
      width: config.width,
      height: config.height,
      decoration: BoxDecoration(
        color: Colors.white,
        border:
            Border.all(color: Colors.grey.shade300, width: config.borderWidth),
        borderRadius: BorderRadius.circular(config.borderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: config.shadowOpacity),
            blurRadius: config.shadowBlur,
            offset: Offset(0, config.shadowOffset),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header
          Column(
            children: [
              // First row: Month dropdown
              Padding(
                padding: const EdgeInsets.only(left: 16.0, top: 16, right: 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      config.titleText,
                      style: TextStyle(
                        fontSize: config.headerFontSize,
                        fontWeight: FontWeight.w500,
                        color: const Color(0xFF434447),
                        fontFamily: 'Test Tiempos Text',
                      ),
                    ),
                    // Month selection dropdown
                    DropdownButton<String>(
                      value: selectedMonth ?? 'July',
                      isDense: true,
                      isExpanded: false,
                      underline: const SizedBox(),
                      icon: const Icon(Icons.keyboard_arrow_down, size: 16),
                      style: TextStyle(
                          fontSize: config.labelFontSize,
                          color: const Color(0xFFA5A5A5),
                          fontFamily: 'Inter',
                          fontWeight: FontWeight.w500),
                      items: const [
                        DropdownMenuItem(
                            value: 'January', child: Text('January')),
                        DropdownMenuItem(
                            value: 'February', child: Text('February')),
                        DropdownMenuItem(value: 'March', child: Text('March')),
                        DropdownMenuItem(value: 'April', child: Text('April')),
                        DropdownMenuItem(value: 'May', child: Text('May')),
                        DropdownMenuItem(value: 'June', child: Text('June')),
                        DropdownMenuItem(value: 'July', child: Text('July')),
                        DropdownMenuItem(
                            value: 'August', child: Text('August')),
                        DropdownMenuItem(
                            value: 'September', child: Text('September')),
                        DropdownMenuItem(
                            value: 'October', child: Text('October')),
                        DropdownMenuItem(
                            value: 'November', child: Text('November')),
                        DropdownMenuItem(
                            value: 'December', child: Text('December')),
                      ],
                      onChanged: (String? newValue) {
                        if (newValue != null && onMonthChanged != null) {
                          onMonthChanged(newValue);
                        }
                      },
                    ),
                  ],
                ),
              ),

              // Second row: Navigation arrows and month title
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  IconButton(
                    onPressed: () {},
                    icon: const Icon(Icons.chevron_left),
                    color: const Color(0xFFA5A5A5),
                    iconSize: config.iconSize,
                  ),
                  // const SizedBox(width: 8),
                  Text(
                    currentMonth,
                    style: TextStyle(
                        fontSize: config.bodyFontSize,
                        fontWeight: FontWeight.w400,
                        color: const Color(0xFF323232),
                        fontFamily: 'Inter'),
                  ),
                  // const SizedBox(width: 8),
                  IconButton(
                    onPressed: () {},
                    icon: const Icon(Icons.chevron_right),
                    color: const Color(0xFFA5A5A5),
                    iconSize: config.iconSize,
                  ),
                ],
              ),
            ],
          ),

          // Weekday labels
          Padding(
            padding: const EdgeInsets.only(left:16.0,right:16.0),
            child: Row(
              children: config.weekdayLabels
                  .map((weekday) => Expanded(
                        child: Text(
                          weekday,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: config.bodyFontSize,
                            fontWeight: FontWeight.w500,
                            fontFamily: 'Inter',
                            color: const Color(0xFF323232),
                          ),
                        ),
                      ))
                  .toList(),
            ),
          ),

          // Calendar grid
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(left:16.0,right: 16.0,bottom:16.0, top:8),
              child: _buildCalendarGrid(
                config: config,
                currentMonth: currentMonth,
                currentYear: currentYear,
                daysInMonth: daysInMonth,
                firstWeekday: firstWeekday,
                selectedDate: selectedDate,
                selectedColor: selectedColor,
                todayColor: todayColor,
                onDateSelected: onDateSelected,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Get size-specific configuration
  static _CalendarConfig _getCalendarConfig(
      CalendarSize size, BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    switch (size) {
      case CalendarSize.small:
        return _CalendarConfig(
          titleText: 'Calendar',
          bodyFontSize: 12,
          width: screenWidth * 0.1405,
          height: screenHeight * 0.3268,
          borderWidth: 0.5,
          borderRadius: 6.0,
          shadowOpacity: 0.06,
          shadowBlur: 4.0,
          shadowOffset: 2.0,
          headerPadding: 12.0,
          headerFontSize: 14.0,
          labelFontSize: 10.0,
          headerText: 'July',
          iconSize: 18.0,
          weekdayPadding: 6.0,
          weekdayFontSize: 10.0,
          weekdayLabels: const ['M', 'T', 'W', 'T', 'F', 'S', 'Su'],
          cellMargin: 1.0,
          cellBorderRadius: 3.0,
          dateFontSize: 10.0,
          dateFontWeight: FontWeight.normal,
        );

      case CalendarSize.medium:
        return _CalendarConfig(
          titleText: 'Calendar',
          labelFontSize: 12.0,
          width: screenWidth * 0.2928,
          height: screenHeight * 0.4700,
          borderWidth: 1.0,
          borderRadius: 16.0,
          shadowOpacity: 0.08,
          shadowBlur: 6.0,
          shadowOffset: 3.0,
          headerPadding: 16.0,
          headerFontSize: 16.0,
          bodyFontSize: 14.0,
          headerText: 'July',
          iconSize: 20.0,
          weekdayPadding: 8.0,
          weekdayFontSize: 12.0,
          weekdayLabels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
          cellMargin: 2.0,
          cellBorderRadius: 4.0,
          dateFontSize: 12.0,
          dateFontWeight: FontWeight.normal,
        );

      case CalendarSize.large:
        return _CalendarConfig(
          titleText: 'Calendar',
          width: screenWidth * 0.6208,
          height: screenHeight * 0.5794,
          borderWidth: 2.0,
          borderRadius: 24.0,
          labelFontSize: 12.0,
          shadowOpacity: 0.12,
          shadowBlur: 8.0,
          shadowOffset: 4.0,
          headerPadding: 20.0,
          headerFontSize: 18.0,
          bodyFontSize: 16.0,
          headerText: 'July',
          iconSize: 24.0,
          weekdayPadding: 10.0,
          weekdayFontSize: 14.0,
          weekdayLabels: [
            'Monday',
            'Tuesday',
            'Wednesday',
            'Thursday',
            'Friday',
            'Saturday',
            'Sunday'
          ],
          cellMargin: 3.0,
          cellBorderRadius: 6.0,
          dateFontSize: 14.0,
          dateFontWeight: FontWeight.w500,
        );
    }
  }

  /// Serialize any widget to complete JSON
  static String widgetToJson(Widget widget, {bool prettyPrint = true}) {
    return FlexibleWidgetSerializer.toJsonString(widget,
        prettyPrint: prettyPrint);
  }

  /// Recreate widget from complete JSON
  static Widget? jsonToWidget(String jsonString) {
    return FlexibleWidgetSerializer.deserializeFromString(jsonString);
  }

  /// Get month index from month name
  static int _getMonthIndex(String monthName) {
    const months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December'
    ];
    return months.indexOf(monthName) + 1;
  }

  /// Build calendar grid with correct dates for selected month
 static Widget _buildCalendarGrid({
  required _CalendarConfig config,
  required String currentMonth,
  required int currentYear,
  required int daysInMonth,
  required int firstWeekday,
  DateTime? selectedDate,
  Color? selectedColor,
  Color? todayColor,
  Function(DateTime)? onDateSelected,
}) {
  final List<Widget> weeks = [];
  int currentDay = 1;

  for (int week = 0; week < 6; week++) {
    final List<Widget> days = [];

    for (int dayOfWeek = 1; dayOfWeek <= 7; dayOfWeek++) {
      Widget dayWidget;

      if (week == 0 && dayOfWeek < firstWeekday) {
        dayWidget = Container(
          decoration: BoxDecoration(
            border: Border(
              top: BorderSide(color: Colors.grey.shade300, width: 0.5),
              left: BorderSide(color: Colors.grey.shade300, width: 0.5),
              right: BorderSide(color: Colors.grey.shade300, width: 0.5),
              bottom: BorderSide(color: Colors.grey.shade300, width: 0.5),
            ),
          ),
        );
      } else if (currentDay > daysInMonth) {
        dayWidget = Container(
          decoration: BoxDecoration(
            border: Border(
              top: BorderSide(color: Colors.grey.shade300, width: 0.5),
              left: BorderSide(color: Colors.grey.shade300, width: 0.5),
              right: BorderSide(color: Colors.grey.shade300, width: 0.5),
              bottom: BorderSide(color: Colors.grey.shade300, width: 0.5),
            ),
          ),
        );
      } else {
        final dayNumber = currentDay;
        final isSelected = selectedDate != null &&
            selectedDate.day == dayNumber &&
            selectedDate.month == _getMonthIndex(currentMonth) &&
            selectedDate.year == currentYear;
        final today = DateTime.now();
        final isToday = today.day == dayNumber &&
            today.month == _getMonthIndex(currentMonth) &&
            today.year == currentYear;

        dayWidget = CalendarDateCell(
          dayNumber: dayNumber,
          isSelected: isSelected,
          isToday: isToday,
          config: config,
          selectedColor: selectedColor,
          todayColor: todayColor,
          onTap: () {
            if (onDateSelected != null) {
              final selectedDateTime =
                  DateTime(currentYear, _getMonthIndex(currentMonth), dayNumber);
              onDateSelected(selectedDateTime);
            }
          },
        );

        currentDay++;
      }

      days.add(Expanded(child: dayWidget));
    }

    weeks.add(
      Expanded(
        child: Row(children: days),
      ),
    );

    if (currentDay > daysInMonth) break;
  }

  return Column(children: weeks);
}

}
