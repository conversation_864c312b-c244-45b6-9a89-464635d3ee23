// import 'package:flutter/material.dart';
// import 'package:bi_directional_widget/bi_directional_widget.dart';



// class RingPieChartDemoScreen extends StatefulWidget {
//   const RingPieChartDemoScreen({super.key});

//   @override
//   State<RingPieChartDemoScreen> createState() => _RingPieChartDemoScreenState();
// }

// class _RingPieChartDemoScreenState extends State<RingPieChartDemoScreen> {
//   ChartSizeType selectedSize = ChartSizeType.small;

//   String propertyType = "Properties - Typography";

//   ChartSizeConfig get currentConfig {
//     switch (selectedSize) {
//       case ChartSizeType.small:
//         return ChartSizeConfig(
//           size: ChartSizeType.small,
//           headingFontSize: 14,
//           bodyFontSize: 12,
//           labelFontSize: 10,
//           chartRadius: 100,
//           propertyType: propertyType,
//           borderThikness: 0.5,
//           borderRadius: 6,
//           elevation: 1.0
//         );
//       case ChartSizeType.medium:
//         return ChartSizeConfig(
//           size: ChartSizeType.medium,
//           headingFontSize: 16,
//           bodyFontSize: 14,
//           labelFontSize: 12,
//           chartRadius: 100,
//           propertyType: propertyType,
//           borderThikness: 1.0,
//           borderRadius: 16,
//           elevation: 1.5
//         );
//       case ChartSizeType.large:
//         return ChartSizeConfig(
//           size: ChartSizeType.large,
//           headingFontSize: 18,
//           bodyFontSize: 16,
//           labelFontSize: 14,
//           chartRadius: 100,
//           propertyType: propertyType,
//           borderThikness: 2.0,
//           borderRadius: 24,
//           elevation: 2.0
//         );
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       body: Column(
//         children: [
//           // Size Selection
//                 const Text('Chart Size:', style: TextStyle(fontWeight: FontWeight.w600)),
// const SizedBox(height: 8),
// Wrap(
//   spacing: 16.0,
//   children: ChartSizeType.values.map((size) {
//     return Row(
//       mainAxisSize: MainAxisSize.min,
//       children: [
//         Radio<ChartSizeType>(
//           value: size,
//           groupValue: selectedSize,
//           onChanged: (value) {
//             if (value != null) {
//               setState(() {
//                 selectedSize = value;
//               });
//             }
//           },
//         ),
//         Text(size.name.toUpperCase()),
//       ],
//     );
//   }).toList(),
// ),
//           // Control Panel
//            Expanded(
//             child: RingPieChartUIBuilder(
//               config: currentConfig,
//               onPressed: () {
//                 // Handle any button press if needed
//                 print('Ring Pie Chart button pressed');
//               },
//             ),
//           ),
         
          
//           // Chart Display
         
//         ],
//       ),
//     );
//   }
// }
