import 'package:flutter/material.dart';
import 'package:ui_controls_library/widgets/ui_builder/common/config.dart';
import 'package:ui_controls_library/widgets/ui_builder/properties_pie_chart.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Chart Styling Demo',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: ChartStylingDemoPage(),
    );
  }
}

class ChartStylingDemoPage extends StatefulWidget {
  @override
  _ChartStylingDemoPageState createState() => _ChartStylingDemoPageState();
}

class _ChartStylingDemoPageState extends State<ChartStylingDemoPage> {
  ChartSizeType _selectedSize = ChartSizeType.medium;
  String _selectedChartType = 'Bar Chart';

  final List<Color> colorList = [
    Colors.blue,
    Colors.green,
    Colors.orange,
    Colors.red,
    Colors.purple,
    Colors.teal,
    Colors.amber,
    Colors.pink,
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Chart Styling Demo - Improved Thickness & Width Control'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Controls
            Row(
              children: [
                Text('Chart Type: '),
                DropdownButton<String>(
                  value: _selectedChartType,
                  items: ['Bar Chart', 'Histogram Chart'].map((String value) {
                    return DropdownMenuItem<String>(
                      value: value,
                      child: Text(value),
                    );
                  }).toList(),
                  onChanged: (String? newValue) {
                    setState(() {
                      _selectedChartType = newValue!;
                    });
                  },
                ),
                SizedBox(width: 20),
                Text('Size: '),
                DropdownButton<ChartSizeType>(
                  value: _selectedSize,
                  items: ChartSizeType.values.map((ChartSizeType value) {
                    return DropdownMenuItem<ChartSizeType>(
                      value: value,
                      child: Text(value.toString().split('.').last.toUpperCase()),
                    );
                  }).toList(),
                  onChanged: (ChartSizeType? newValue) {
                    setState(() {
                      _selectedSize = newValue!;
                    });
                  },
                ),
              ],
            ),
            SizedBox(height: 20),
            
            // Chart display
            Text(
              'Chart with Improved Styling:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 10),
            
            // Container to show the chart doesn't expand to full width
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey[300]!),
                borderRadius: BorderRadius.circular(8),
              ),
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Container shows chart width is controlled (not full width)',
                    style: TextStyle(color: Colors.grey[600], fontSize: 12),
                  ),
                  SizedBox(height: 10),
                  _buildChart(),
                ],
              ),
            ),
            
            SizedBox(height: 20),
            
            // Information panel
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Improvements Made:',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                  ),
                  SizedBox(height: 8),
                  Text('✓ Charts now use SizedBox for controlled width (like calendar widget)'),
                  Text('✓ Bar thickness significantly increased for better visibility'),
                  Text('✓ Size-responsive dimensions: Small (280/240), Medium (400/360), Large (520/480)'),
                  Text('✓ Graphic package bars use ratios: 1.2, 1.4, 1.6 for thick appearance'),
                  Text('✓ FL Chart histograms use pixel widths: 32, 40, 48 for substantial bars'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChart() {
    final config = ChartSizeConfig(
      size: _selectedSize,
      headingFontSize: _selectedSize == ChartSizeType.small ? 14 : 
                      _selectedSize == ChartSizeType.medium ? 16 : 18,
      bodyFontSize: _selectedSize == ChartSizeType.small ? 12 : 
                    _selectedSize == ChartSizeType.medium ? 14 : 16,
      labelFontSize: _selectedSize == ChartSizeType.small ? 10 : 
                     _selectedSize == ChartSizeType.medium ? 12 : 14,
      chartRadius: 100,
      propertyType: "Properties",
      borderThikness: 1,
      borderRadius: 6,
      elevation: 0,
      chartType: _selectedChartType == 'Bar Chart' ? ChartType.bar : ChartType.histogram,
    );

    return RingPieChartUIBuilder(
      config: config,
      colorList: colorList,
      onPressed: () {
        print('${_selectedChartType} button pressed');
      },
    );
  }
}
