import 'package:flutter_test/flutter_test.dart';
import 'package:ui_controls_library/widgets/ui_builder/common/config.dart';
import 'package:ui_controls_library/widgets/ui_builder/common/size_responsive_config.dart';

void main() {
  group('SizeResponsiveConfig Tests', () {
    test('should return correct font sizes for different chart sizes', () {
      // Test small size
      final smallFontConfig = SizeResponsiveConfig.getFontSizeConfig(ChartSizeType.small);
      expect(smallFontConfig.heading, 16.0);
      expect(smallFontConfig.body, 12.0);
      expect(smallFontConfig.label, 10.0);

      // Test medium size
      final mediumFontConfig = SizeResponsiveConfig.getFontSizeConfig(ChartSizeType.medium);
      expect(mediumFontConfig.heading, 20.0);
      expect(mediumFontConfig.body, 14.0);
      expect(mediumFontConfig.label, 12.0);

      // Test large size
      final largeFontConfig = SizeResponsiveConfig.getFontSizeConfig(ChartSizeType.large);
      expect(largeFontConfig.heading, 24.0);
      expect(largeFontConfig.body, 16.0);
      expect(largeFontConfig.label, 14.0);
    });

    test('should return correct border configurations for different chart sizes', () {
      // Test small size - based on provided images
      final smallBorderConfig = SizeResponsiveConfig.getBorderConfig(ChartSizeType.small);
      expect(smallBorderConfig.width, 0.5);
      expect(smallBorderConfig.radius, 6.0);

      // Test medium size - based on provided images
      final mediumBorderConfig = SizeResponsiveConfig.getBorderConfig(ChartSizeType.medium);
      expect(mediumBorderConfig.width, 1.0);
      expect(mediumBorderConfig.radius, 16.0);

      // Test large size - based on provided images
      final largeBorderConfig = SizeResponsiveConfig.getBorderConfig(ChartSizeType.large);
      expect(largeBorderConfig.width, 2.0);
      expect(largeBorderConfig.radius, 24.0);
    });

    test('should return correct scatter chart configurations', () {
      // Test scatter chart small size
      final smallScatterConfig = SizeResponsiveConfig.getChartConfig(ChartType.scatter, ChartSizeType.small);
      expect(smallScatterConfig.pointSize, 6.0);
      expect(smallScatterConfig.chartHeight, 200.0);

      // Test scatter chart medium size
      final mediumScatterConfig = SizeResponsiveConfig.getChartConfig(ChartType.scatter, ChartSizeType.medium);
      expect(mediumScatterConfig.pointSize, 8.0);
      expect(mediumScatterConfig.chartHeight, 280.0);

      // Test scatter chart large size
      final largeScatterConfig = SizeResponsiveConfig.getChartConfig(ChartType.scatter, ChartSizeType.large);
      expect(largeScatterConfig.pointSize, 10.0);
      expect(largeScatterConfig.chartHeight, 350.0);
    });

    test('should return correct bar chart configurations', () {
      // Test bar chart small size
      final smallBarConfig = SizeResponsiveConfig.getChartConfig(ChartType.bar, ChartSizeType.small);
      expect(smallBarConfig.barWidth, 35.0);
      expect(smallBarConfig.chartHeight, 200.0);

      // Test bar chart medium size
      final mediumBarConfig = SizeResponsiveConfig.getChartConfig(ChartType.bar, ChartSizeType.medium);
      expect(mediumBarConfig.barWidth, 45.0);
      expect(mediumBarConfig.chartHeight, 280.0);

      // Test bar chart large size
      final largeBarConfig = SizeResponsiveConfig.getChartConfig(ChartType.bar, ChartSizeType.large);
      expect(largeBarConfig.barWidth, 55.0);
      expect(largeBarConfig.chartHeight, 350.0);
    });

    test('should create complete ChartSizeConfig with all responsive properties', () {
      // Test creating a complete config for scatter chart, medium size
      final config = SizeResponsiveConfig.createConfig(
        size: ChartSizeType.medium,
        chartType: ChartType.scatter,
        propertyType: 'Scatter Plot',
        elevation: 2.0,
      );

      expect(config.size, ChartSizeType.medium);
      expect(config.chartType, ChartType.scatter);
      expect(config.propertyType, 'Scatter Plot');
      expect(config.elevation, 2.0);
      
      // Check font sizes
      expect(config.headingFontSize, 20.0);
      expect(config.bodyFontSize, 14.0);
      expect(config.labelFontSize, 12.0);
      
      // Check border properties
      expect(config.borderThikness, 1.0);
      expect(config.borderRadius, 16.0);
    });

    test('should handle default values for unknown chart types', () {
      // Test with an unknown chart type - should return default config
      final unknownConfig = SizeResponsiveConfig.getChartConfig(ChartType.bubble, ChartSizeType.small);
      expect(unknownConfig.chartHeight, 200.0);
      expect(unknownConfig.pointSize, null); // Should be null for bubble chart
      expect(unknownConfig.barWidth, null); // Should be null for bubble chart
    });
  });
}
