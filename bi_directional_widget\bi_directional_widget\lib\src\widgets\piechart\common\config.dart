enum ChartSizeType { small, medium, large }

class ChartSizeConfig {
  final ChartSizeType? size;
  final double? headingFontSize;
  final double? bodyFontSize;
  final double? labelFontSize;
  final double? chartRadius;
  final String? propertyType;
  final double? borderThikness;
  final double borderRadius;
  final double elevation;

  const ChartSizeConfig({
    this.size,
    this.headingFontSize,
    this.bodyFontSize,
    this.labelFontSize,
    this.chartRadius,
    this.propertyType,
    this.borderThikness,
    this.borderRadius=6,
    this.elevation=0
  });

  ChartSizeConfig copyWith({
    ChartSizeType? size,
    double? headingFontSize,
    double? bodyFontSize,
    double? labelFontSize,
    double? chartRadius,
    String? propertyType,
    double? borderThikness,
    double? borderRadius,
    double? elevation,
  }) {
    return ChartSizeConfig(
      size: size ?? this.size,
      headingFontSize: headingFontSize ?? this.headingFontSize,
      bodyFontSize: bodyFontSize ?? this.bodyFontSize,
      labelFontSize: labelFontSize ?? this.labelFontSize,
      chartRadius: chartRadius ?? this.chartRadius,
      propertyType: propertyType ?? this.propertyType,
      borderThikness: borderThikness ?? this.borderThikness,
      borderRadius: borderRadius??this.borderRadius,
      elevation: elevation??this.elevation
    );
  }
}
