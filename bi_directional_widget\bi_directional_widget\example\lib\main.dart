import 'package:bi_directional_widget_example/comprehensive_widget_demo.dart';
import 'package:flutter/material.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      title: 'Bi-Directional Widget Demo',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: const ComprehensiveWidgetDemo(),
    );
  }
}
