import 'config.dart';

/// Configuration class for size-responsive properties across all chart types
class SizeResponsiveConfig {
  // Private constructor to prevent instantiation
  SizeResponsiveConfig._();

  /// Font size configurations
  static const Map<ChartSizeType, FontSizeConfig> _fontSizes = {
    ChartSizeType.small: FontSizeConfig(
      heading: 16.0,
      body: 12.0,
      label: 10.0,
    ),
    ChartSizeType.medium: FontSizeConfig(
      heading: 20.0,
      body: 14.0,
      label: 12.0,
    ),
    ChartSizeType.large: FontSizeConfig(
      heading: 24.0,
      body: 16.0,
      label: 14.0,
    ),
  };

  /// Border configurations
  static const Map<ChartSizeType, BorderConfig> _borderConfigs = {
    ChartSizeType.small: BorderConfig(
      width: 0.5,
      radius: 6.0,
    ),
    ChartSizeType.medium: BorderConfig(
      width: 1.0,
      radius: 16.0,
    ),
    ChartSizeType.large: BorderConfig(
      width: 2.0,
      radius: 24.0,
    ),
  };

  /// Chart-specific configurations
  static const Map<ChartType, Map<ChartSizeType, ChartSpecificConfig>> _chartConfigs = {
    ChartType.scatter: {
      ChartSizeType.small: ChartSpecificConfig(
        pointSize: 6.0,
        chartHeight: 200.0,
        gridLineWidth: 0.5,
        axisLabelFontSize: 10.0,
        axisTitleFontSize: 11.0,
      ),
      ChartSizeType.medium: ChartSpecificConfig(
        pointSize: 8.0,
        chartHeight: 280.0,
        gridLineWidth: 0.8,
        axisLabelFontSize: 12.0,
        axisTitleFontSize: 13.0,
      ),
      ChartSizeType.large: ChartSpecificConfig(
        pointSize: 10.0,
        chartHeight: 350.0,
        gridLineWidth: 1.0,
        axisLabelFontSize: 14.0,
        axisTitleFontSize: 15.0,
      ),
    },
    ChartType.bar: {
      ChartSizeType.small: ChartSpecificConfig(
        barWidth: 35.0,
        chartHeight: 200.0,
        gridLineWidth: 1.0,
        axisLabelFontSize: 10.0,
        axisTitleFontSize: 11.0,
      ),
      ChartSizeType.medium: ChartSpecificConfig(
        barWidth: 45.0,
        chartHeight: 280.0,
        gridLineWidth: 1.2,
        axisLabelFontSize: 12.0,
        axisTitleFontSize: 13.0,
      ),
      ChartSizeType.large: ChartSpecificConfig(
        barWidth: 55.0,
        chartHeight: 350.0,
        gridLineWidth: 1.5,
        axisLabelFontSize: 14.0,
        axisTitleFontSize: 15.0,
      ),
    },
    ChartType.histogram: {
      ChartSizeType.small: ChartSpecificConfig(
        barWidth: 32.0,
        chartHeight: 200.0,
        gridLineWidth: 1.0,
        axisLabelFontSize: 10.0,
        axisTitleFontSize: 11.0,
      ),
      ChartSizeType.medium: ChartSpecificConfig(
        barWidth: 40.0,
        chartHeight: 280.0,
        gridLineWidth: 1.2,
        axisLabelFontSize: 12.0,
        axisTitleFontSize: 13.0,
      ),
      ChartSizeType.large: ChartSpecificConfig(
        barWidth: 48.0,
        chartHeight: 350.0,
        gridLineWidth: 1.5,
        axisLabelFontSize: 14.0,
        axisTitleFontSize: 15.0,
      ),
    },
    ChartType.ring: {
      ChartSizeType.small: ChartSpecificConfig(
        outerRadius: '85%',
        innerRadius: '65%',
      ),
      ChartSizeType.medium: ChartSpecificConfig(
        outerRadius: '95%',
        innerRadius: '65%',
      ),
      ChartSizeType.large: ChartSpecificConfig(
        outerRadius: '100%',
        innerRadius: '65%',
      ),
    },
    ChartType.disc: {
      ChartSizeType.small: ChartSpecificConfig(
        outerRadius: '85%',
      ),
      ChartSizeType.medium: ChartSpecificConfig(
        outerRadius: '95%',
      ),
      ChartSizeType.large: ChartSpecificConfig(
        outerRadius: '100%',
      ),
    },
    ChartType.bubble: {
      ChartSizeType.small: ChartSpecificConfig(
        chartHeight: 200.0,
      ),
      ChartSizeType.medium: ChartSpecificConfig(
        chartHeight: 280.0,
      ),
      ChartSizeType.large: ChartSpecificConfig(
        chartHeight: 350.0,
      ),
    },
  };

  /// Get font size configuration for a given size
  static FontSizeConfig getFontSizeConfig(ChartSizeType size) {
    return _fontSizes[size] ?? _fontSizes[ChartSizeType.medium]!;
  }

  /// Get border configuration for a given size
  static BorderConfig getBorderConfig(ChartSizeType size) {
    return _borderConfigs[size] ?? _borderConfigs[ChartSizeType.medium]!;
  }

  /// Get chart-specific configuration for a given chart type and size
  static ChartSpecificConfig getChartConfig(ChartType chartType, ChartSizeType size) {
    return _chartConfigs[chartType]?[size] ?? 
           ChartSpecificConfig(); // Return default config if not found
  }

  /// Create a complete ChartSizeConfig with all responsive properties
  static ChartSizeConfig createConfig({
    required ChartSizeType size,
    required ChartType chartType,
    String? propertyType,
    double? elevation,
  }) {
    final fontConfig = getFontSizeConfig(size);
    final borderConfig = getBorderConfig(size);
    final chartConfig = getChartConfig(chartType, size);

    return ChartSizeConfig(
      size: size,
      headingFontSize: fontConfig.heading,
      bodyFontSize: fontConfig.body,
      labelFontSize: fontConfig.label,
      borderThikness: borderConfig.width,
      borderRadius: borderConfig.radius,
      chartType: chartType,
      propertyType: propertyType,
      elevation: elevation ?? 0,
      chartRadius: chartConfig.outerRadius != null ? 
                   double.tryParse(chartConfig.outerRadius!.replaceAll('%', '')) : null,
    );
  }
}

/// Font size configuration class
class FontSizeConfig {
  final double heading;
  final double body;
  final double label;

  const FontSizeConfig({
    required this.heading,
    required this.body,
    required this.label,
  });
}

/// Border configuration class
class BorderConfig {
  final double width;
  final double radius;

  const BorderConfig({
    required this.width,
    required this.radius,
  });
}

/// Chart-specific configuration class
class ChartSpecificConfig {
  final double? pointSize;
  final double? barWidth;
  final double? chartHeight;
  final double? gridLineWidth;
  final double? axisLabelFontSize;
  final double? axisTitleFontSize;
  final String? outerRadius;
  final String? innerRadius;

  const ChartSpecificConfig({
    this.pointSize,
    this.barWidth,
    this.chartHeight,
    this.gridLineWidth,
    this.axisLabelFontSize,
    this.axisTitleFontSize,
    this.outerRadius,
    this.innerRadius,
  });
}
