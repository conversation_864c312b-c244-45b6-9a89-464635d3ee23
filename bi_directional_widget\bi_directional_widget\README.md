# Bi-Directional Widget

A Flutter package for bidirectional Widget ↔ JSON conversion, enabling dynamic widget serialization and deserialization to reduce project load.

## Features

- **Widget → JSON**: Convert any supported widget instance to JSON configuration
- **JSON → Widget**: Create widget instances from JSON configuration  
- **Widget Introspection**: Automatically extract widget properties
- **Schema Validation**: Ensure JSON configurations are valid
- **Type Safety**: Maintain Flutter's type safety while enabling dynamic creation
- **Extensible**: Easy to add support for new widget types

## Supported Widgets

- **Calendar Widget**: Full-featured calendar with multiple size variants (small, medium, large)
- More widgets coming soon...

## Installation

Add this to your package's `pubspec.yaml` file:

```yaml
dependencies:
  bi_directional_widget: ^1.0.0
```

## Usage

### Basic Widget ↔ JSON Conversion

```dart
import 'package:bi_directional_widget/bi_directional_widget.dart';

// Create a calendar widget
final calendar = CalendarWidget(
  size: CalendarSize.medium,
  selectedDate: DateTime.now(),
  borderRadius: 16.0,
  // ... other properties
);

// Convert widget to JSON
final json = calendar.toJson();
print(json); // Outputs complete JSON configuration

// Create widget from JSON
final recreatedCalendar = CalendarWidget.fromJson(json);

// Convert to JSON string for storage/transmission
final jsonString = calendar.toJsonString(prettyPrint: true);

// Recreate from JSON string
final fromString = CalendarWidget.fromJsonString(jsonString);
```

### Using the Conversion Service

```dart
import 'package:bi_directional_widget/bi_directional_widget.dart';

// Register widget converters
final registry = WidgetRegistryService();
registry.register(CalendarWidgetConverter());

// Convert any registered widget to JSON
final json = WidgetToJsonService.convert(myWidget);

// Create widget from JSON
final widget = JsonToWidgetService.convert(json);
```

### Calendar Widget Example

```dart
// Create calendar with specific configuration
final calendar = CalendarWidget(
  size: CalendarSize.large,
  initialSelectedDate: DateTime(2024, 6, 15),
  borderRadius: 24.0,
  borderWidth: 2.0,
  hasShadow: true,
  selectedDateColor: Colors.blue,
  todayColor: Colors.orange,
  events: {
    DateTime(2024, 6, 10): [
      CalendarEvent(title: 'Meeting', color: Colors.red),
    ],
  },
);

// Get JSON representation
final config = calendar.toJson();

// The JSON will contain all configuration:
// {
//   "widgetType": "calendar",
//   "size": "large", 
//   "initialSelectedDate": "2024-06-15T00:00:00.000",
//   "borderRadius": 24.0,
//   "borderWidth": 2.0,
//   "hasShadow": true,
//   "selectedDateColor": "#0000FF",
//   "todayColor": "#FFA500",
//   "events": {...},
//   ...
// }
```

## Architecture

### Core Components

1. **WidgetJsonConverter**: Abstract base class for widget converters
2. **JsonSerializableWidget**: Interface for widgets supporting JSON conversion
3. **WidgetRegistryService**: Central registry for widget converters
4. **ValidationService**: JSON schema validation
5. **IntrospectionService**: Automatic property extraction

### Widget Implementation Pattern

To add a new widget type:

1. Implement `JsonSerializableWidget` interface
2. Add `fromJson` factory constructor
3. Implement `toJson` method
4. Create converter class extending `WidgetJsonConverter`
5. Register converter with `WidgetRegistryService`

## Benefits

- **Reduced Bundle Size**: Store widget configurations as JSON instead of code
- **Dynamic UI**: Create UIs from server-side configurations
- **A/B Testing**: Switch widget configurations without app updates
- **Theming**: Apply themes by modifying JSON configurations
- **Debugging**: Inspect and modify widget configurations at runtime
- **Persistence**: Save and restore UI states easily

## Contributing

Contributions are welcome! Please read our contributing guidelines and submit pull requests for any improvements.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
