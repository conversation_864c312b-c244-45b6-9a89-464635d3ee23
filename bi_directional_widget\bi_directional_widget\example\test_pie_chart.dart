// import 'package:flutter/material.dart';
// import 'package:bi_directional_widget/bi_directional_widget.dart';
// import 'package:pie_chart/pie_chart.dart';
// import 'dart:convert';

// void main() {
//   // Test data for pie chart
//   final Map<String, double> dataMap = {
//     'Point 1': 1, 'Point 2': 1, 'Point 3': 1, 'Point 4': 1,
//   };

//   final List<Color> colorList = [
//     const Color(0xFF0D47A1), const Color(0xFF1565C0), 
//     const Color(0xFF1976D2), const Color(0xFF1E88E5),
//   ];

//   // Create a pie chart widget
//   final pieChart = PieChart(
//     dataMap: dataMap,
//     colorList: colorList,
//     chartType: ChartType.ring,
//     chartRadius: 80,
//     ringStrokeWidth: 16,
//     centerWidget: const Text(
//       "Total Value\n\$9,999.99",
//       textAlign: TextAlign.center,
//       style: TextStyle(
//         fontWeight: FontWeight.bold,
//         fontSize: 10,
//       ),
//     ),
//     legendOptions: const LegendOptions(showLegends: false),
//     chartValuesOptions: const ChartValuesOptions(showChartValues: false),
//   );

//   print("=== Testing PieChart Serialization ===");
  
//   try {
//     // Serialize the pie chart
//     final serialized = FlexibleWidgetSerializer.serialize(pieChart);
//     final jsonString = const JsonEncoder.withIndent('  ').convert(serialized);
    
//     print("✅ Serialization successful!");
//     print("JSON Output:");
//     print(jsonString);
    
//     // Test deserialization
//     final deserializedWidget = FlexibleWidgetSerializer.deserialize(serialized);
    
//     if (deserializedWidget != null && deserializedWidget is PieChart) {
//       print("✅ Deserialization successful! Widget type: ${deserializedWidget.runtimeType}");
//       print("✅ Chart type: ${deserializedWidget.chartType}");
//       print("✅ Chart radius: ${deserializedWidget.chartRadius}");
//       print("✅ Data map: ${deserializedWidget.dataMap}");
//     } else {
//       print("❌ Deserialization failed or returned wrong type: ${deserializedWidget?.runtimeType}");
//     }
    
//   } catch (e) {
//     print("❌ Error during serialization/deserialization: $e");
//   }
  
//   print("=== Test Complete ===");
// }
